"""
增强的权限验证装饰器
"""
from functools import wraps
from typing import Union, List, Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from config.role_constants import RoleConstants, PermissionConstants
from exceptions.exception import PermissionException

class EnhancedAuth:
    """增强的权限验证装饰器"""
    
    @staticmethod
    def require_permission(permission: str):
        """要求特定权限"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从kwargs中获取当前用户
                current_user = None
                for key, value in kwargs.items():
                    if isinstance(value, CurrentUserModel):
                        current_user = value
                        break
                
                if not current_user:
                    raise PermissionException(message='未登录')
                
                # 超级管理员直接通过
                if PermissionConstants.ALL_PERMISSION in current_user.permissions:
                    return await func(*args, **kwargs)
                
                # 检查权限
                if permission not in current_user.permissions:
                    raise PermissionException(message=f'权限不足，需要权限：{permission}')
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    @staticmethod
    def require_menu_access(menu: str):
        """要求菜单访问权限"""
        return EnhancedAuth.require_permission(f'menu:{menu}')
    
    @staticmethod
    def require_any_permission(permissions: List[str]):
        """要求任意一个权限"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                current_user = None
                for key, value in kwargs.items():
                    if isinstance(value, CurrentUserModel):
                        current_user = value
                        break
                
                if not current_user:
                    raise PermissionException(message='未登录')
                
                # 超级管理员直接通过
                if PermissionConstants.ALL_PERMISSION in current_user.permissions:
                    return await func(*args, **kwargs)
                
                # 检查是否拥有任意一个权限
                has_permission = any(perm in current_user.permissions for perm in permissions)
                if not has_permission:
                    raise PermissionException(message=f'权限不足，需要权限之一：{", ".join(permissions)}')
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator

class ProjectAuth:
    """项目权限验证"""
    
    @staticmethod
    def require_project_access(require_manager: bool = False, allow_owner: bool = True):
        """要求项目访问权限"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                current_user = None
                db = None
                project_id = None
                
                # 提取参数
                for key, value in kwargs.items():
                    if isinstance(value, CurrentUserModel):
                        current_user = value
                    elif isinstance(value, AsyncSession):
                        db = value
                    elif key == 'project_id':
                        project_id = value
                
                if not all([current_user, db]):
                    raise PermissionException(message='参数不足')
                
                # 超级管理员直接通过
                if PermissionConstants.ALL_PERMISSION in current_user.permissions:
                    return await func(*args, **kwargs)
                
                # 如果需要项目ID但没有提供，检查基础权限
                if project_id is None:
                    if require_manager:
                        if PermissionConstants.PROJECT_EDIT not in current_user.permissions:
                            raise PermissionException(message='无项目管理权限')
                    else:
                        if PermissionConstants.PROJECT_LIST not in current_user.permissions:
                            raise PermissionException(message='无项目访问权限')
                    return await func(*args, **kwargs)
                
                # 检查项目访问权限
                has_access = await ProjectAuthService.check_project_access(
                    db, current_user.user.user_id, project_id, require_manager, allow_owner
                )
                
                if not has_access:
                    raise PermissionException(message='无项目访问权限')
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator

class TaskAuth:
    """任务权限验证"""
    
    @staticmethod
    def require_task_access(action: str = 'view'):
        """要求任务访问权限"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                current_user = None
                db = None
                task_id = None
                
                # 提取参数
                for key, value in kwargs.items():
                    if isinstance(value, CurrentUserModel):
                        current_user = value
                    elif isinstance(value, AsyncSession):
                        db = value
                    elif key == 'task_id':
                        task_id = value
                
                if not all([current_user, db]):
                    raise PermissionException(message='参数不足')
                
                # 超级管理员直接通过
                if PermissionConstants.ALL_PERMISSION in current_user.permissions:
                    return await func(*args, **kwargs)
                
                # 如果没有任务ID，检查基础权限
                if task_id is None:
                    required_perm = {
                        'view': PermissionConstants.TASK_LIST,
                        'create': PermissionConstants.TASK_CREATE,
                        'edit': PermissionConstants.TASK_EDIT_OWN,
                        'delete': PermissionConstants.TASK_DELETE_OWN,
                        'execute': PermissionConstants.TASK_EXECUTE,
                        'download': PermissionConstants.TASK_DOWNLOAD
                    }.get(action, PermissionConstants.TASK_LIST)
                    
                    if required_perm not in current_user.permissions:
                        raise PermissionException(message=f'无任务{action}权限')
                    return await func(*args, **kwargs)
                
                # 检查任务访问权限
                has_access = await TaskAuthService.check_task_access(
                    db, current_user.user.user_id, task_id, action
                )
                
                if not has_access:
                    raise PermissionException(message=f'无任务{action}权限')
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator

class ProjectAuthService:
    """项目权限服务"""
    
    @staticmethod
    async def check_project_access(db: AsyncSession, user_id: int, 
                                 project_id: int, require_manager: bool = False,
                                 allow_owner: bool = True) -> bool:
        """检查项目访问权限"""
        try:
            from module_business.dao.projects_dao import ProjectsDao
            from module_business.dao.project_members_dao import ProjectMembersDao
            
            # 检查是否是项目负责人
            if allow_owner:
                project = await ProjectsDao.get_projects_detail_by_id(db, project_id)
                if project and hasattr(project, 'owner_id') and project.owner_id == user_id:
                    return True
            
            # 检查项目成员权限
            try:
                member_info = await ProjectMembersDao.get_project_member_info(db, project_id, user_id)
                if member_info:
                    if require_manager:
                        return member_info.role_type == 'manager'
                    return True
            except:
                # 如果项目成员表不存在或查询失败，返回False
                pass
            
            return False
        except Exception:
            return False

class TaskAuthService:
    """任务权限服务"""
    
    @staticmethod
    async def check_task_access(db: AsyncSession, user_id: int, 
                              task_id: int, action: str = 'view') -> bool:
        """检查任务访问权限"""
        try:
            from module_business.dao.tasks_dao import TasksDao
            
            task = await TasksDao.get_tasks_detail_by_id(db, task_id)
            if not task:
                return False
            
            # 检查是否是任务创建者
            if hasattr(task, 'create_by_id') and task.create_by_id == user_id:
                return True
            
            # 对于查看权限，检查项目权限
            if action == 'view':
                return await ProjectAuthService.check_project_access(
                    db, user_id, task.project_id, require_manager=False
                )
            
            # 对于编辑、删除权限，需要项目管理员权限或任务创建者
            elif action in ['edit', 'delete']:
                return await ProjectAuthService.check_project_access(
                    db, user_id, task.project_id, require_manager=True
                )
            
            # 对于执行、下载权限，项目成员即可
            elif action in ['execute', 'download']:
                return await ProjectAuthService.check_project_access(
                    db, user_id, task.project_id, require_manager=False
                )
            
            return False
        except Exception:
            return False

# 兼容性装饰器，保持与现有代码的兼容
class CheckUserInterfaceAuth:
    """兼容原有的权限验证装饰器"""
    
    def __init__(self, perm: Union[str, List], is_strict: bool = False):
        self.perm = perm
        self.is_strict = is_strict

    def __call__(self, current_user: CurrentUserModel = Depends(LoginService.get_current_user)):
        user_auth_list = current_user.permissions
        if '*:*:*' in user_auth_list:
            return True
        if isinstance(self.perm, str):
            if self.perm in user_auth_list:
                return True
        if isinstance(self.perm, list):
            if self.is_strict:
                if all([perm_str in user_auth_list for perm_str in self.perm]):
                    return True
            else:
                if any([perm_str in user_auth_list for perm_str in self.perm]):
                    return True
        raise PermissionException(data='', message='该用户无此接口权限')

class CheckRoleInterfaceAuth:
    """兼容原有的角色验证装饰器"""
    
    def __init__(self, role_key: Union[str, List], is_strict: bool = False):
        self.role_key = role_key
        self.is_strict = is_strict

    def __call__(self, current_user: CurrentUserModel = Depends(LoginService.get_current_user)):
        user_role_list = current_user.user.role
        user_role_key_list = [role.role_key for role in user_role_list]
        if isinstance(self.role_key, str):
            if self.role_key in user_role_key_list:
                return True
        if isinstance(self.role_key, list):
            if self.is_strict:
                if all([role_key_str in user_role_key_list for role_key_str in self.role_key]):
                    return True
            else:
                if any([role_key_str in user_role_key_list for role_key_str in self.role_key]):
                    return True
        raise PermissionException(data='', message='该用户无此接口权限')