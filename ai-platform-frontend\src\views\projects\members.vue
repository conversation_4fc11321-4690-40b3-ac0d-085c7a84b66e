<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft">返回项目</el-button>
        <h2>{{ projectName }} - 成员管理</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="openAddMemberDialog">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
      </div>
    </div>

    <!-- 成员列表 -->
    <el-card class="member-list-card">
      <template #header>
        <div class="card-header">
          <span>项目成员 ({{ members.length }})</span>
          <el-button link @click="refreshMembers">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="members" v-loading="loading" style="width: 100%">
        <el-table-column prop="user_name" label="用户名" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.avatar">{{ (row.user_name || 'U')?.charAt(0) }}</el-avatar>
              <span class="user-name">{{ row.user_name || 'Unknown' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="nick_name" label="昵称" width="150">
          <template #default="{ row }">
            <span>{{ row.nick_name || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="email" label="邮箱" width="200">
          <template #default="{ row }">
            <span>{{ row.email || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="role_type" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role_type)" size="small">
              {{ getRoleText(row.role_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="加入时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="warning"
              @click="changeRole(row)"
              :disabled="!!row.is_owner"
              v-hasPermi="['project:member:edit']"
            >
              修改角色
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="removeMember(row)"
              :disabled="!!row.is_owner"
              v-hasPermi="['project:member:remove']"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addMemberDialogVisible"
      title="添加项目成员"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="addMemberForm" :rules="addMemberRules" ref="addMemberFormRef" label-width="80px">
        <el-form-item label="选择用户" prop="userId">
          <el-select
            v-model="addMemberForm.userId"
            placeholder="请选择要添加的用户"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.user_id"
              :label="`${user.user_name || 'Unknown'} (${user.nick_name || 'No nickname'})`"
              :value="user.user_id"
            >
              <div class="user-option">
                <el-avatar :size="24">{{ (user.user_name || 'U')?.charAt(0) }}</el-avatar>
                <span>{{ user.user_name || 'Unknown' }}</span>
                <span class="user-nickname">({{ user.nick_name || 'No nickname' }})</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="角色" prop="roleType">
          <el-radio-group v-model="addMemberForm.roleType">
            <el-radio value="member">普通成员</el-radio>
            <el-radio value="manager">管理员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addMemberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addMember" :loading="addMemberLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改角色对话框 -->
    <el-dialog
      v-model="changeRoleDialogVisible"
      title="修改成员角色"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="changeRoleForm" ref="changeRoleFormRef" label-width="80px">
        <el-form-item label="用户">
          <span>{{ selectedMember?.user_name }} ({{ selectedMember?.nick_name }})</span>
        </el-form-item>
        
        <el-form-item label="角色">
          <el-radio-group v-model="changeRoleForm.roleType">
            <el-radio value="member">普通成员</el-radio>
            <el-radio value="manager">管理员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="changeRoleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmChangeRole" :loading="changeRoleLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, ArrowLeft, UserFilled } from '@element-plus/icons-vue'
import { getProjectDetail } from '@/api/projects'
import { getProjectMembers, getAvailableUsers, addProjectMember, updateProjectMemberRole, removeProjectMember } from '@/api/project-members'
import request from '@/utils/request'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const members = ref([])
const projectName = ref('')
const projectId = route.params.id

// 添加成员相关
const addMemberDialogVisible = ref(false)
const addMemberLoading = ref(false)
const addMemberForm = ref({
  userId: '',
  roleType: 'member'
})
const addMemberRules = {
  userId: [{ required: true, message: '请选择用户', trigger: 'change' }],
  roleType: [{ required: true, message: '请选择角色', trigger: 'change' }]
}
const availableUsers = ref([])
const addMemberFormRef = ref()

// 修改角色相关
const changeRoleDialogVisible = ref(false)
const changeRoleLoading = ref(false)
const selectedMember = ref(null)
const changeRoleForm = ref({
  roleType: 'member'
})
const changeRoleFormRef = ref()

// 获取项目信息
const fetchProjectInfo = async () => {
  try {
    const response = await getProjectDetail(projectId)
    if (response.code === 200) {
      // Handle both camelCase and snake_case field names
      projectName.value = response.data.projectName || response.data.project_name || '未知项目'
    }
  } catch (error) {
    console.error('获取项目信息失败:', error)
  }
}

// 获取项目成员列表
const fetchMembers = async () => {
  loading.value = true
  try {
    // 检查 projectId 是否有效
    if (!projectId) {
      console.error('项目ID不存在，无法获取成员列表')
      ElMessage.error('项目ID不存在，无法获取成员列表')
      members.value = []
      return
    }
    
    // 确保projectId是整数类型
    const projectIdInt = parseInt(projectId)
    if (isNaN(projectIdInt)) {
      console.error('项目ID格式不正确')
      ElMessage.error('项目ID格式不正确')
      members.value = []
      return
    }
    
    // 调用接口
    const response = await getProjectMembers(projectIdInt)
    // 检查响应
    if (response && response.code === 200) {
      members.value = Array.isArray(response.data) ? response.data : []
    } else {
      const msg = response?.msg || '获取项目成员失败'
      console.error('获取项目成员失败:', msg)
      ElMessage.error(msg)
      members.value = []
    }
  } catch (error) {
    // 打印详细错误
    console.error('获取项目成员失败:', error)
    let errMsg = '获取项目成员失败'
    if (error?.message) {
      errMsg += `: ${error.message}`
    }
    ElMessage.error(errMsg)
    members.value = []
  } finally {
    loading.value = false
  }
}

// 获取可添加的用户列表
const fetchAvailableUsers = async () => {
  try {
    if (!projectId) {
      availableUsers.value = []
      ElMessage.error('项目ID不存在，无法获取可添加用户列表')
      return
    }
    
    // 确保projectId是整数类型
    const projectIdInt = parseInt(projectId)
    if (isNaN(projectIdInt)) {
      availableUsers.value = []
      ElMessage.error('项目ID格式不正确')
      return
    }
    
    // 使用新的可选择用户API
    const response = await request({
      url: '/business/project-members/selectable-users',
      method: 'get'
    })
    if (response.code === 200) {
      availableUsers.value = response.data || []
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 刷新成员列表
const refreshMembers = () => {
  fetchMembers()
}

// 打开添加成员对话框
const openAddMemberDialog = () => {
  addMemberForm.value = {
    userId: '',
    roleType: 'member' // 固定
  }
  addMemberDialogVisible.value = true
  // 只有 projectId 有效才请求
  if (projectId) {
    const projectIdInt = parseInt(projectId)
    if (!isNaN(projectIdInt)) {
      fetchAvailableUsers()
    } else {
      availableUsers.value = []
      ElMessage.error('项目ID格式不正确，无法获取可添加用户列表')
    }
  } else {
    availableUsers.value = []
    ElMessage.error('项目ID不存在，无法获取可添加用户列表')
  }
}

// 添加成员
const addMember = async () => {
  if (!addMemberFormRef.value) return
  try {
    await addMemberFormRef.value.validate()
    addMemberLoading.value = true
    
    // 确保projectId是整数类型
    const projectIdInt = parseInt(projectId)
    if (isNaN(projectIdInt)) {
      ElMessage.error('项目ID格式不正确')
      return
    }
    
    // 传递用户选择的角色类型
    const response = await addProjectMember(projectIdInt, addMemberForm.value.userId, addMemberForm.value.roleType)
    if (response.code === 200) {
      ElMessage.success('添加成员成功')
      addMemberDialogVisible.value = false
      fetchMembers()
    } else {
      ElMessage.error(response.msg || '添加成员失败')
    }
  } catch (error) {
    console.error('添加成员失败:', error)
    ElMessage.error('添加成员失败')
  } finally {
    addMemberLoading.value = false
  }
}

// 修改角色
const changeRole = (member) => {
  selectedMember.value = member
  changeRoleForm.value.roleType = member.role_type
  changeRoleDialogVisible.value = true
}

// 确认修改角色
const confirmChangeRole = async () => {
  try {
    changeRoleLoading.value = true
    
    // 确保projectId是整数类型
    const projectIdInt = parseInt(projectId)
    if (isNaN(projectIdInt)) {
      ElMessage.error('项目ID格式不正确')
      return
    }
    
    const response = await updateProjectMemberRole(projectIdInt, selectedMember.value.user_id, changeRoleForm.value.roleType)
    
    if (response.code === 200) {
      ElMessage.success('修改角色成功')
      changeRoleDialogVisible.value = false
      fetchMembers()
    } else {
      ElMessage.error(response.msg || '修改角色失败')
    }
  } catch (error) {
    console.error('修改角色失败:', error)
    ElMessage.error('修改角色失败')
  } finally {
    changeRoleLoading.value = false
  }
}

// 移除成员
const removeMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员 "${member.user_name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 确保projectId是整数类型
    const projectIdInt = parseInt(projectId)
    if (isNaN(projectIdInt)) {
      ElMessage.error('项目ID格式不正确')
      return
    }
    
    const response = await removeProjectMember(projectIdInt, member.user_id)
    
    if (response.code === 200) {
      ElMessage.success('移除成员成功')
      fetchMembers()
    } else {
      ElMessage.error(response.msg || '移除成员失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      ElMessage.error('移除成员失败')
    }
  }
}

// 返回项目页面
const goBack = () => {
  router.push({ name: 'ProjectDetail', params: { id: projectId } })
}

// 工具函数
const getRoleTagType = (roleType) => {
  switch (roleType) {
    case 'owner':
      return 'danger'
    case 'manager':
      return 'warning'
    case 'member':
      return 'info'
    default:
      return 'info'
  }
}

const getRoleText = (roleType) => {
  switch (roleType) {
    case 'owner':
      return '项目所有者'
    case 'manager':
      return '管理员'
    case 'member':
      return '普通成员'
    default:
      return '普通成员'
  }
}

const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchProjectInfo()
  fetchMembers()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.member-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-nickname {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table .el-table__row) {
  cursor: default;
}

:deep(.el-avatar) {
  background-color: #409eff;
  color: white;
  font-weight: bold;
}
</style>