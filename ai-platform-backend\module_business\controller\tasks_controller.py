from fastapi import APIRouter, Depends, Form, Request
from pydantic_validation_decorator import Validate<PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.tasks_service import TasksService
from module_business.entity.vo.tasks_vo import TasksModel, TasksPageQueryModel
from module_business.service.files_service import FilesService
from utils.common_util import CamelCaseUtil
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime
import json


tasksController = APIRouter(prefix='/business/tasks', dependencies=[Depends(LoginService.get_current_user)])


@tasksController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:tasks:list'))]
)
async def get_business_tasks_list(
    request: Request,
tasks_page_query: TasksPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    tasks_page_query_result = await TasksService.get_tasks_list_services(query_db, tasks_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=tasks_page_query_result)


@tasksController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:tasks:add'))]
@ValidateFields(validate_model='add_tasks')
@Log(title='任务信息管理', business_type=BusinessType.INSERT)
async def add_business_tasks(
    request: Request,
    add_tasks: TasksModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_tasks.created_at = datetime.now()
    add_tasks.updated_at = datetime.now()
    # 设置任务创建者为当前用户
    add_tasks.assigned_to = current_user.user.user_id
    
    add_tasks_result = await TasksService.add_tasks_services(query_db, add_tasks)
    logger.info(add_tasks_result.message)

    result_obj = add_tasks_result.result
    if result_obj:
        transformed = CamelCaseUtil.transform_result(result_obj)
        data = TasksModel(**transformed).model_dump(by_alias=True)
    else:
        data = {}

    return ResponseUtil.success(msg=add_tasks_result.message, data=data)


@tasksController.put('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:tasks:edit'))]
@ValidateFields(validate_model='edit_tasks')
@Log(title='任务信息管理', business_type=BusinessType.UPDATE)
async def edit_business_tasks(
    request: Request,
    edit_tasks: TasksModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # edit_tasks.update_by = current_user.user.user_name
    edit_tasks.updated_at = datetime.now()
    edit_tasks_result = await TasksService.edit_tasks_services(query_db, edit_tasks)

    # 删除任务关联文件
    if(edit_tasks.is_deleted):  
        files_list = await FilesService.get_files_by_task_id(query_db, edit_tasks.task_id)
        for file in files_list:
            # 兼容：如果是ORM对象则转为Pydantic模型
            from module_business.entity.vo.files_vo import FilesModel
            if not hasattr(file, 'model_dump'):
                # ORM对象转Pydantic，避免懒加载触发MissingGreenlet错误
                file_data = dict(file.__dict__)
                file_data.pop('_sa_instance_state', None)  # 移除SQLAlchemy内部状态
                
                # 如果 file_id 不在字典中或为None，跳过处理
                if 'file_id' not in file_data or file_data.get('file_id') is None:
                    logger.warning(f"file_id 不存在或为None，跳过文件删除")
                    continue
                
                # 创建FilesModel并处理file_id可能丢失的问题
                actual_file_id = file_data.get('file_id')
                file_vo = FilesModel(**file_data)
                
                # 修复：如果file_vo.file_id为None，手动设置
                if file_vo.file_id is None and actual_file_id is not None:
                    file_vo.file_id = actual_file_id
                
                file_vo.is_deleted = 1
                await FilesService.edit_files_services(query_db, file_vo)
            else:
                # 已是Pydantic对象
                await FilesService.edit_files_services(query_db, file)

        logger.info(f"删除任务{edit_tasks.task_id}关联文件成功")

    logger.info(edit_tasks_result.message)

    return ResponseUtil.success(msg=edit_tasks_result.message)

@tasksController.get(
    '/{task_id}', response_model=TasksModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:tasks:query'))]
)
async def query_detail_business_tasks(
    request: Request, 
    task_id: int, 
    query_db: AsyncSession = Depends(get_db),
    dashboard: bool = False
):
    """
    获取任务详情
    
    Args:
        task_id: 任务ID
        dashboard: 是否为看板模式，如果是则返回扩展数据
        
    Returns:
        任务详情数据
    """
    if dashboard:
        # 看板模式，返回扩展数据
        try:
            detail_data = await TasksService.get_task_dashboard_detail_services(query_db, task_id)
            if detail_data is None:
                return ResponseUtil.error(msg="任务不存在")
            return ResponseUtil.success(data=detail_data, msg="获取任务详情成功")
        except Exception as e:
            logger.error(f"获取任务详情失败: {e}")
            return ResponseUtil.error(msg=f"获取任务详情失败: {str(e)}")
    else:
        # 原有模式，返回简单详情（包含Redis缓存的最新进度）
        redis_client = request.app.state.redis
        tasks_detail_result = await TasksService.tasks_detail_services(query_db, task_id, redis_client)
        logger.info(f'获取task_id为{task_id}的信息成功')
        return ResponseUtil.success(data=tasks_detail_result)


@tasksController.get(
    '/list/{project_id}', response_model=List[TasksModel]  # dependencies=[Depends(CheckUserInterfaceAuth('business:tasks:query'))]
)
async def query_tasks_list_by_project(
    request: Request,
    project_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    tasks_list_result = await TasksService.get_tasks_list_by_project_id_services(query_db, project_id)
    logger.info(f'获取project_id为{project_id}的所有任务信息成功')
    return ResponseUtil.success(data=tasks_list_result)


@tasksController.get('/progress/{task_id}')
async def get_task_latest_progress(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取任务的最新进度（优先从Redis缓存获取）
    """
    try:
        # 先尝试从Redis获取最新进度和消息
        redis_client = request.app.state.redis
        progress_key = f"task_progress:{task_id}"
        cached_data = await redis_client.get(progress_key)

        if cached_data:
            try:
                import json
                progress_data = json.loads(cached_data)
                return ResponseUtil.success(data={
                    "task_id": task_id,
                    "progress": progress_data.get("progress", 0),
                    "message": progress_data.get("message", "从缓存获取最新进度"),
                    "source": "redis_cache",
                    "timestamp": progress_data.get("timestamp")
                })
            except (json.JSONDecodeError, TypeError):
                # 兼容旧格式（纯数字）
                return ResponseUtil.success(data={
                    "task_id": task_id,
                    "progress": int(cached_data),
                    "source": "redis_cache",
                    "message": "从缓存获取最新进度"
                })

        # Redis中没有缓存，从数据库获取
        task_info = await TasksService.tasks_detail_services(query_db, task_id)
        if task_info.task_id:
            return ResponseUtil.success(data={
                "task_id": task_id,
                "progress": task_info.progress or 0,
                "source": "database",
                "message": "从数据库获取进度"
            })
        else:
            return ResponseUtil.failure(msg="任务不存在")

    except Exception as e:
        logger.error(f"获取任务进度失败: {e}")
        return ResponseUtil.failure(msg=f"获取任务进度失败: {str(e)}")


@tasksController.post(
    '/check-historical-match',
    # dependencies=[Depends(CheckUserInterfaceAuth('business:tasks:add'))]
)
async def check_historical_match(
    request: Request,
    project_id: int = Form(..., description="项目ID"),
    type_id: int = Form(..., description="任务类型ID"),
    tool_id: int = Form(..., description="工具ID"),
    parameters: str = Form(..., description="任务参数JSON字符串"),
    query_db: AsyncSession = Depends(get_db),
):
    """
    检查历史任务匹配（除了选型任务都需要调用）
    """
    try:
        # 解析参数JSON
        params_dict = json.loads(parameters) if parameters else {}

        # 先验证参数是否为空
        if not params_dict:
            return ResponseUtil.failure(msg="任务参数为空，请先配置任务参数后再检查历史匹配")

        # 检查参数是否为空字典
        if not isinstance(params_dict, dict) or len(params_dict) == 0:
            return ResponseUtil.failure(msg="任务参数为空，请先配置任务参数后再检查历史匹配")

        # 进行详细的参数验证
        from module_business.entity.vo.tasks_vo import TasksModel
        temp_task = TasksModel(
            project_id=project_id,
            task_name="临时任务",  # 临时名称，仅用于验证
            type_id=type_id,
            tool_id=tool_id,
            parameters=params_dict
        )

        try:
            await TasksService._validate_task_parameters(temp_task)
        except Exception as e:
            error_msg = str(e)
            if "不能为空" in error_msg:
                return ResponseUtil.failure(msg=f"任务参数不完整：{error_msg}。请填写所有必需的参数后再检查历史匹配。")
            else:
                return ResponseUtil.failure(msg=f"任务参数验证失败：{error_msg}。请检查参数格式是否正确。")

        # 参数验证通过后，再检查历史匹配
        historical_task = await TasksService.check_historical_match(
            query_db, project_id, type_id, tool_id, params_dict
        )

        if historical_task:
            logger.info(f'找到历史匹配任务: {historical_task["task_id"]}')
            return ResponseUtil.success(
                data={
                    'has_match': True,
                    'historical_task': historical_task,
                    'message': '已在历史数据中匹配到相同的任务'
                }
            )
        else:
            return ResponseUtil.success(
                data={
                    'has_match': False,
                    'message': '未找到匹配的历史任务'
                }
            )

    except Exception as e:
        logger.error(f'检查历史任务匹配失败: {e}')
        return ResponseUtil.error(msg=f'检查历史任务匹配失败: {str(e)}')
