from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.tasks_dao import TasksDao
from module_business.entity.vo.tasks_vo import DeleteTasksModel, TasksModel, TasksPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.log_util import logger


class TasksService:
    """
    任务信息管理模块服务层
    """

    @classmethod
    async def get_tasks_list_services(
        cls, query_db: AsyncSession, query_object: TasksPageQueryModel, is_page: bool = False
    ):
        """
        获取任务信息管理列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 任务信息管理列表信息对象
        """
        tasks_list_result = await TasksDao.get_tasks_list(query_db, query_object, is_page)

        return tasks_list_result


    @classmethod
    async def check_historical_match(cls, query_db: AsyncSession, project_id: int, type_id: int, tool_id: int, parameters: dict):
        """
        检查历史任务是否存在相同的project_id、tool_id和parameters

        :param query_db: orm对象
        :param project_id: 项目ID
        :param type_id: 任务类型ID
        :param tool_id: 工具ID
        :param parameters: 任务参数
        :return: 匹配的历史任务信息或None
        """
        try:
            # 选型任务不需要检查历史匹配
            if type_id == 3:  # type_id=3是选型任务
                return None

            # 计算参数的MD5值
            parameters_md5 = await cls.calculate_parameters_md5(parameters)

            # 查询相同project_id、tool_id、type_id和parameters_md5的已完成任务
            from module_business.dao.tasks_dao import TasksDao
            historical_tasks = await TasksDao.get_completed_tasks_by_criteria(
                query_db, project_id, type_id, tool_id, parameters_md5
            )

            if not historical_tasks:
                return None

            # 找到第一个匹配的任务，返回任务信息
            if historical_tasks:
                task = historical_tasks[0]  # 取最新的一个匹配任务
                return {
                    'task_id': task.task_id,
                    'task_name': task.task_name,
                    'project_id': task.project_id,
                    'parameters': task.parameters,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at,
                    'progress': task.progress,
                    'status': task.status,
                    'is_deleted': 0
                }

            return None

        except Exception as e:
            logger.error(f"检查历史任务匹配失败: {e}")
            return None

    @classmethod
    async def _validate_task_parameters(cls, task_object: TasksModel):
        """
        验证任务参数是否为空

        :param task_object: 任务对象
        :raises ServiceException: 当参数验证失败时抛出异常
        """
        from exceptions.exception import ServiceException

        # 1. 首先检查是否有参数
        if not task_object.parameters:
            raise ServiceException(message='任务参数为空，请配置任务参数')

        # 2. 检查参数是否为空字典
        if not isinstance(task_object.parameters, dict) or len(task_object.parameters) == 0:
            raise ServiceException(message='任务参数为空，请配置任务参数')

        # 3. 检查参数值是否为空
        empty_params = []
        for key, value in task_object.parameters.items():
            # 跳过一些可以为空的系统字段和文件路径字段（这些字段在上传后会被填充）
            skip_fields = {
                'created_at', 'updated_at', 'uploadPath', 'filePath',
                'submitted_at', 'started_at', 'completed_at',
                # 文件MD5字段可以为空，因为会在后续计算
                'modelFilePath_md5', 'model_file_path_md5', 'modelFile_md5'
            }
            if key in skip_fields:
                continue

            # 检查值是否为空
            if value is None or (isinstance(value, str) and value.strip() == ''):
                empty_params.append(key)

        if empty_params:
            # 提供更友好的参数名称映射
            friendly_names = cls._get_friendly_parameter_names()
            friendly_empty_params = []
            for param in empty_params:
                friendly_name = friendly_names.get(param, param)
                friendly_empty_params.append(friendly_name)

            raise ServiceException(message=f'以下参数不能为空: {", ".join(friendly_empty_params)}')

    @classmethod
    def _get_friendly_parameter_names(cls) -> dict:
        """
        获取参数的友好名称映射

        :return: 参数名称映射字典
        """
        return {
            # 仿真任务参数
            'operatingTemp': '工况温度',
            'inletAbsolutePressure': '入口绝对压力',
            'inletMassFlow': '入口质量流量',
            'oRingMaterial': 'O型圈材质',
            'coreMaterial': '阀芯材质',
            'valveDiameter': '阀口直径',
            'pipeDiameter': '管路内径',
            'sealAngle': '密封斜度',
            'iterationStep': '迭代步长',
            'maxSteps': '最大步数',
            'fluidMedium': '流体工质',
            'modelFilePath': '模型文件',

            # 选型任务参数
            'flowRate': '流量',
            'pressure': '压力',
            'temperature': '温度',
            'medium': '介质',
            'pipeSize': '管径',
            'material': '材质',
            'diameter': '直径',

            # 通用参数
            'taskName': '任务名称',
            'description': '任务描述',
            'projectId': '项目ID',
            'toolId': '工具ID',
            'typeId': '任务类型'
        }

    @classmethod
    async def calculate_parameters_md5(cls, parameters: dict) -> str:
        """
        计算参数的MD5值，对于仿真任务需要包含模型文件的MD5值

        :param parameters: 参数字典
        :return: MD5哈希值
        """
        import hashlib
        import json

        # 需要忽略的动态参数（文件路径等，但保留模型文件MD5）
        ignore_keys = {'uploadPath', 'filePath', 'created_at', 'updated_at'}

        # 过滤掉忽略的键
        filtered_params = {k: v for k, v in parameters.items() if k not in ignore_keys}

        # 处理模型文件MD5值
        # 首先检查是否已经有MD5值（从前端传来的）
        model_file_keys = ['modelFilePath', 'model_file_path', 'modelFile']
        for key in model_file_keys:
            md5_key = f'{key}_md5'
            if key in filtered_params and filtered_params[key]:
                # 如果已经有MD5值，直接使用
                if md5_key in filtered_params:
                    # 移除原始文件路径，保留MD5值
                    del filtered_params[key]
                    logger.info(f"使用前端提供的文件MD5: {md5_key} = {filtered_params[md5_key]}")
                else:
                    # 如果没有MD5值，尝试计算
                    file_path = filtered_params[key]
                    try:
                        file_md5 = await cls._get_file_md5(file_path)
                        if file_md5:
                            # 用文件MD5替换文件路径
                            filtered_params[md5_key] = file_md5
                            # 移除原始文件路径
                            del filtered_params[key]
                            logger.info(f"计算得到文件MD5: {md5_key} = {file_md5}")
                    except Exception as e:
                        logger.warning(f"获取文件MD5失败 {file_path}: {e}")
                        # 如果获取文件MD5失败，保留原始路径进行比较
                        pass

        # 对参数进行排序，确保相同的参数生成相同的MD5值
        sorted_params = json.dumps(filtered_params, sort_keys=True)

        # 计算MD5
        return hashlib.md5(sorted_params.encode('utf-8')).hexdigest()

    @classmethod
    async def _get_file_md5(cls, file_path: str) -> str:
        """
        获取文件的MD5值

        :param file_path: 文件路径
        :return: 文件MD5值
        """
        import hashlib
        import os
        from utils.minio_util import MinioUtil

        try:
            # 如果是MinIO路径，从MinIO下载文件计算MD5
            if file_path.startswith('kb_'):
                minio_util = MinioUtil.get_instance()
                file_content = await minio_util.download_file_by_path(file_path)
                return hashlib.md5(file_content).hexdigest()

            # 如果是本地文件路径
            elif os.path.exists(file_path):
                hash_md5 = hashlib.md5()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
                return hash_md5.hexdigest()

            # 如果是HTTP URL，需要下载文件计算MD5
            elif file_path.startswith('http'):
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get(file_path) as response:
                        if response.status == 200:
                            content = await response.read()
                            return hashlib.md5(content).hexdigest()

            return None

        except Exception as e:
            logger.error(f"计算文件MD5失败 {file_path}: {e}")
            return None

    @classmethod
    def _compare_parameters(cls, params1: dict, params2: dict) -> bool:
        """
        比较两个参数字典是否相同（忽略文件路径等动态参数）

        :param params1: 参数字典1
        :param params2: 参数字典2
        :return: 是否匹配
        """
        if not params1 or not params2:
            return params1 == params2

        # 需要忽略的动态参数（文件路径等）
        ignore_keys = {'modelFilePath', 'uploadPath', 'filePath', 'created_at', 'updated_at'}

        # 过滤掉忽略的键
        filtered_params1 = {k: v for k, v in params1.items() if k not in ignore_keys}
        filtered_params2 = {k: v for k, v in params2.items() if k not in ignore_keys}

        return filtered_params1 == filtered_params2

    @classmethod
    async def copy_task_results(cls, query_db: AsyncSession, source_task_id: int, target_task_id: int):
        """
        复制历史任务的结果和文件信息到新任务

        :param query_db: orm对象
        :param source_task_id: 源任务ID
        :param target_task_id: 目标任务ID
        :return: 复制结果
        """
        try:
            from module_business.dao.files_dao import FilesDao
            from module_business.entity.vo.files_vo import FilesModel
            from datetime import datetime

            # 1. 复制任务相关的文件记录
            source_files = await FilesDao.get_files_by_task_id(query_db, source_task_id)

            copied_files = []
            for file_record in source_files:
                # 创建新的文件记录，关联到新任务
                new_file = FilesModel(
                    kb_id=file_record.kb_id,
                    project_id=file_record.project_id,
                    file_type=file_record.file_type,
                    original_name=file_record.original_name,
                    file_path=file_record.file_path,
                    file_size=file_record.file_size,
                    source_task_id=target_task_id,  # 关联到新任务
                    created_at=datetime.now()
                )

                copied_file = await FilesDao.add_files_dao(query_db, new_file)
                copied_files.append(copied_file)

            # 2. 更新目标任务状态为已完成
            from module_business.dao.tasks_dao import TasksDao
            update_data = {
                'task_id': target_task_id,
                'status': 'completed',
                'progress': 100,
                'updated_at': datetime.now()
            }

            await TasksDao.edit_tasks_dao(query_db, update_data)
            await query_db.commit()

            return {
                'success': True,
                'copied_files_count': len(copied_files),
                'message': '历史任务结果复制成功'
            }

        except Exception as e:
            await query_db.rollback()
            logger.error(f"复制任务结果失败: {e}")
            return {
                'success': False,
                'message': f'复制任务结果失败: {str(e)}'
            }

    @classmethod
    async def add_tasks_services(cls, query_db: AsyncSession, page_object: TasksModel):
        """
        新增任务信息管理信息service

        :param query_db: orm对象
        :param page_object: 新增任务信息管理对象
        :return: 新增任务信息管理校验结果
        """
        try:
            # 验证任务参数
            await cls._validate_task_parameters(page_object)

            # 计算参数的MD5值并设置到对象中
            if page_object.parameters:
                page_object.parameters_md5 = await cls.calculate_parameters_md5(page_object.parameters)

            # 正常创建任务（历史匹配检查已在前端处理）
            db_task = await TasksDao.add_tasks_dao(query_db, page_object)

            # Extract attributes from the ORM object BEFORE the commit
            task_dict = {
                'task_id': db_task.task_id,
                'project_id': db_task.project_id,
                'task_name': db_task.task_name,
                'type_id': db_task.type_id,
                'description': db_task.description,
                'assigned_to': db_task.assigned_to,
                'status': db_task.status,
                'progress': db_task.progress,
                'tool_id': db_task.tool_id,
                'parameters': db_task.parameters,
                'created_at': db_task.created_at,
                'updated_at': db_task.updated_at,
                'started_at': db_task.started_at,
                'completed_at': db_task.completed_at,
                'is_deleted': db_task.is_deleted,
                'average_execution_time': db_task.average_execution_time
            }
            
            # 添加工具信息和用户信息
            if db_task.tool_id:
                from sqlalchemy import select
                from module_business.entity.do.tools_do import RdTools
                
                # 获取工具信息
                tool_query = select(RdTools).where(RdTools.tool_id == db_task.tool_id)
                tool_result = await query_db.execute(tool_query)
                tool_info = tool_result.scalars().first()
                
                if tool_info:
                    task_dict['tool_name'] = tool_info.tool_name
                    task_dict['tool_queue_required'] = bool(tool_info.queue_required)
                else:
                    task_dict['tool_name'] = '未知工具'
                    task_dict['tool_queue_required'] = True
            else:
                task_dict['tool_name'] = '未选择工具'
                task_dict['tool_queue_required'] = True
            
            # 获取用户信息
            if db_task.assigned_to:
                from sqlalchemy import select
                from module_admin.entity.do.user_do import SysUser
                
                user_query = select(SysUser).where(SysUser.user_id == db_task.assigned_to)
                user_result = await query_db.execute(user_query)
                user_info = user_result.scalars().first()
                
                if user_info:
                    task_dict['creator_name'] = user_info.nick_name or user_info.user_name
                else:
                    task_dict['creator_name'] = '未知用户'
            else:
                task_dict['creator_name'] = '未知用户'

            await query_db.commit()

            # 确保事务完全提交后再返回，避免队列和数据库不同步
            import asyncio
            await asyncio.sleep(0.1)  # 等待100毫秒确保事务提交

            return CrudResponseModel(is_success=True, message='新增成功', result=task_dict)
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_tasks_services(cls, query_db: AsyncSession, page_object: TasksModel):
        """
        编辑任务信息管理信息service

        :param query_db: orm对象
        :param page_object: 编辑任务信息管理对象
        :return: 编辑任务信息管理校验结果
        """
        edit_tasks = page_object.model_dump(exclude_unset=True, exclude={})
        tasks_info = await cls.tasks_detail_services(query_db, page_object.task_id)
        if tasks_info.task_id:
            # 如果更新了parameters字段，需要验证参数
            if 'parameters' in edit_tasks and edit_tasks['parameters'] is not None:
                await cls._validate_task_parameters(page_object)

            try:
                await TasksDao.edit_tasks_dao(query_db, edit_tasks)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='任务信息管理不存在')

    @classmethod
    async def delete_tasks_services(cls, query_db: AsyncSession, page_object: DeleteTasksModel):
        """
        删除任务信息管理信息service

        :param query_db: orm对象
        :param page_object: 删除任务信息管理对象
        :return: 删除任务信息管理校验结果
        """
        if page_object.task_ids:
            task_id_list = page_object.task_ids.split(',')
            try:
                for task_id in task_id_list:
                    await TasksDao.delete_tasks_dao(query_db, TasksModel(taskId=task_id))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入为空')

    @classmethod
    async def tasks_detail_services(cls, query_db: AsyncSession, task_id: int, redis_client=None):
        """
        获取任务信息管理详细信息service

        :param query_db: orm对象
        :param task_id:
        :param redis_client: Redis客户端（可选，用于获取最新进度）
        :return: 对应的信息
        """
        tasks = await TasksDao.get_tasks_detail_by_id(query_db, task_id=task_id)
        if tasks:
            result = TasksModel(**CamelCaseUtil.transform_result(tasks))

            # 如果任务正在运行且提供了Redis客户端，尝试获取最新进度
            if redis_client and result.status == 'running':
                try:
                    import json
                    progress_key = f"task_progress:{task_id}"
                    cached_data = await redis_client.get(progress_key)
                    if cached_data:
                        try:
                            progress_info = json.loads(cached_data)
                            cached_progress = progress_info.get("progress", 0)
                        except (json.JSONDecodeError, TypeError):
                            # 兼容旧格式（纯数字）
                            cached_progress = int(cached_data)

                        # 如果Redis中的进度比数据库中的新，使用Redis的进度
                        if cached_progress > (result.progress or 0):
                            logger.info(f"使用Redis缓存的最新进度: 任务{task_id} {result.progress}% -> {cached_progress}%")
                            result.progress = cached_progress
                except Exception as e:
                    logger.warning(f"获取Redis缓存进度失败: {e}")
        else:
            result = TasksModel(**dict())

        return result

    @classmethod
    async def get_tasks_list_by_project_id_services(cls, query_db: AsyncSession, project_id: int):
        """
        根据项目ID获取所有任务信息service

        :param query_db: orm对象
        :param project_id: 项目ID
        :return: 项目关联的所有任务信息列表
        """
        tasks_list = await TasksDao.get_tasks_list_by_project_id(query_db, project_id)
        result = []
        
        # 收集所有唯一的工具ID和用户ID
        tool_ids = set()
        user_ids = set()
        for task in tasks_list:
            if task.tool_id:
                tool_ids.add(task.tool_id)
            if task.assigned_to:
                user_ids.add(task.assigned_to)
        
        # 批量获取工具信息
        tools_info = {}
        tools_name_info = {}
        if tool_ids:
            from sqlalchemy import select
            from module_business.entity.do.tools_do import RdTools
            
            query = select(RdTools).where(RdTools.tool_id.in_(tool_ids))
            result_tools = await query_db.execute(query)
            tools = result_tools.scalars().all()
            
            for tool in tools:
                tools_info[tool.tool_id] = bool(tool.queue_required)
                tools_name_info[tool.tool_id] = tool.tool_name
        
        # 批量获取用户信息
        users_info = {}
        if user_ids:
            from sqlalchemy import select
            from module_admin.entity.do.user_do import SysUser
            
            query = select(SysUser).where(SysUser.user_id.in_(user_ids))
            result_users = await query_db.execute(query)
            users = result_users.scalars().all()
            
            for user in users:
                users_info[user.user_id] = user.nick_name or user.user_name
        
        # 构建结果，添加工具队列要求信息和用户信息
        for task in tasks_list:
            task_dict = CamelCaseUtil.transform_result(task)
            
            # 设置工具队列要求信息
            if task.tool_id and task.tool_id in tools_info:
                task_dict['tool_queue_required'] = tools_info[task.tool_id]
            else:
                task_dict['tool_queue_required'] = True  # 默认需要队列
            
            # 设置工具名称信息
            if task.tool_id and task.tool_id in tools_name_info:
                task_dict['tool_name'] = tools_name_info[task.tool_id]
            else:
                task_dict['tool_name'] = '未知工具'
            
            # 设置用户信息
            if task.assigned_to and task.assigned_to in users_info:
                task_dict['creator_name'] = users_info[task.assigned_to]
            else:
                task_dict['creator_name'] = '未知用户'
            
            result.append(TasksModel(**task_dict))
        
        return result

    @classmethod
    async def get_task_dashboard_detail_services(cls, query_db: AsyncSession, task_id: int):
        """
        获取任务看板详情service（包含扩展数据）

        :param query_db: orm对象
        :param task_id: 任务ID
        :return: 任务看板详情数据
        """
        from datetime import datetime, timedelta
        import json
        import asyncio
        from sqlalchemy import select
        from module_business.entity.do.knowledge_bases_do import RdKnowledgeBases
        from module_business.dao.files_dao import FilesDao
        
        # 获取任务基本信息
        task_info = await TasksDao.get_tasks_detail_by_id(query_db, task_id)
        if not task_info:
            return None
        
        # 获取任务关联的知识库ID
        kb_id = None
        if task_info.project_id:
            query = (
                select(RdKnowledgeBases)
                .where(
                    RdKnowledgeBases.project_id == task_info.project_id,
                    RdKnowledgeBases.is_deleted == 0
                )
                .order_by(RdKnowledgeBases.kb_id)
                .limit(1)
            )
            
            result = await query_db.execute(query)
            kb_obj = result.scalars().first()
            if kb_obj:
                kb_id = kb_obj.kb_id
        
        # 计算任务进度
        progress = 0
        estimated_completion = None
        
        if task_info.status == "pending":
            progress = 0
            estimated_completion = task_info.created_at + timedelta(minutes=30) if task_info.created_at else None
        elif task_info.status == "running":
            # 修改进度计算逻辑，避免基于30秒时间间隔的进度计算
            if task_info.created_at:
                elapsed = datetime.now() - task_info.created_at
                # 使用更合理的进度计算，基于实际执行时间而不是固定30秒间隔
                # 假设任务平均执行时间为5分钟，避免30秒就显示90%进度
                total_estimated_seconds = 300  # 5分钟
                progress = min(int((elapsed.total_seconds() / total_estimated_seconds) * 90), 90)
            else:
                progress = 10  # 默认进度
            estimated_completion = task_info.created_at + timedelta(minutes=25) if task_info.created_at else None
        elif task_info.status == "completed":
            progress = 100
            estimated_completion = task_info.completed_at
        elif task_info.status == "failed":
            progress = 0
            estimated_completion = None
        
        # 解析任务参数
        task_parameters = {}
        if task_info.parameters:
            try:
                task_parameters = json.loads(task_info.parameters) if isinstance(task_info.parameters, str) else task_info.parameters
            except:
                task_parameters = task_info.parameters if task_info.parameters else {}
        
        # 获取结果文件（按任务ID查询所有类型的文件）
        result_files = []
        try:
            # 直接按任务ID查询文件，而不是通过知识库查询
            files = await asyncio.wait_for(
                FilesDao.get_files_by_task_id(query_db, task_id),
                timeout=5.0
            )
            for file in files:
                result_files.append({
                    "file_id": file.file_id,
                    "file_name": file.original_name,
                    "file_path": file.storage_path,
                    "file_type": file.file_type,
                    "file_size": file.file_size,
                    "source_task_id": file.source_task_id,
                    "created_at": file.created_at.isoformat() if file.created_at else None
                })
        except asyncio.TimeoutError:
            logger.warning(f"获取任务结果文件超时，跳过文件查询")
        except Exception as e:
            logger.warning(f"获取任务结果文件失败: {e}")
        
        # 构建返回数据
        return {
            "task_id": task_info.task_id,
            "task_name": task_info.task_name,
            "status": task_info.status,
            "progress": progress,
            "start_time": task_info.created_at.isoformat() if task_info.created_at else None,
            "estimated_completion": estimated_completion.isoformat() if estimated_completion else None,
            "completed_at": task_info.completed_at.isoformat() if task_info.completed_at else None,
            "parameters": task_parameters,
            "result_files": result_files,
            "kb_id": kb_id
        }

    @staticmethod
    async def export_tasks_list_services(tasks_list: List):
        """
        导出任务信息管理信息service

        :param tasks_list: 任务信息管理信息列表
        :return: 任务信息管理信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'taskId': '',
            'projectId': '',
            'taskName': '',
            'typeId': '引用system_types',
            'description': '',
            'assignedTo': '',
            'status': '',
            'progress': '',
            'toolId': '',
            'parameters': '',
            'createdAt': '',
            'updatedAt': '',
            'startedAt': '',
            'completedAt': '',
            'isDeleted': '',
            'averageExecutionTime': '工具平均执行时间',
        }
        binary_data = ExcelUtil.export_list2excel(tasks_list, mapping_dict)

        return binary_data
