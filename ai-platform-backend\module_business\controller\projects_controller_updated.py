"""
项目管理控制器 - 应用新的权限控制
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.aspect.enhanced_auth import EnhancedAuth, ProjectAuth
from module_admin.aspect.data_scope_enhanced import DataScopeFilter
from module_business.entity.vo.projects_vo import (
    ProjectPageQueryModel, AddProjectModel, EditProjectModel, DeleteProjectModel
)
from module_business.service.projects_service import ProjectsService
from utils.response_util import ResponseUtil
from utils.log_util import logger
from config.role_constants import PermissionConstants

projectController = APIRouter()

@projectController.get('/list')
@EnhancedAuth.require_menu_access('project')
async def get_project_list(
    request: Request,
    query_params: ProjectPageQueryModel = Depends(ProjectPageQueryModel.as_query()),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取项目列表 - 应用数据权限过滤"""
    try:
        # 应用数据权限过滤
        projects_result = await ProjectsService.get_project_list_services(
            query_db, query_params, current_user
        )
        logger.info('获取项目列表成功')
        return ResponseUtil.success(data=projects_result)
    except Exception as e:
        logger.error(f'获取项目列表失败: {e}')
        return ResponseUtil.error(msg=f'获取项目列表失败: {str(e)}')

@projectController.post('/add')
@EnhancedAuth.require_permission(PermissionConstants.PROJECT_CREATE)
async def add_project(
    request: Request,
    add_project: AddProjectModel,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """新增项目"""
    try:
        # 设置创建者
        add_project.create_by = current_user.user.user_name
        add_project.owner_id = current_user.user.user_id
        
        add_project_result = await ProjectsService.add_project_services(query_db, add_project)

        # 自动将创建者添加为项目成员（manager角色）
        from module_business.service.project_members_service import ProjectMembersService
        success, message = await ProjectMembersService.add_project_member(
            query_db,
            add_project_result.result.project_id,
            current_user.user.user_id,
            'manager',  # 创建者默认为manager角色
            current_user.user.user_id
        )

        if not success:
            logger.warning(f'添加项目创建者为成员失败: {message}')

        # 提交所有操作
        await query_db.commit()

        logger.info('新增项目成功')
        return ResponseUtil.success(data=add_project_result, msg=add_project_result.message)
    except Exception as e:
        logger.error(f'新增项目失败: {e}')
        return ResponseUtil.error(msg=f'新增项目失败: {str(e)}')

@projectController.put('/{project_id}')
@ProjectAuth.require_project_access(require_manager=True)
async def edit_project(
    request: Request,
    project_id: int,
    edit_project: EditProjectModel,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """编辑项目 - 需要项目管理员权限"""
    try:
        edit_project.project_id = project_id
        edit_project.update_by = current_user.user.user_name
        
        edit_project_result = await ProjectsService.edit_project_services(query_db, edit_project)
        logger.info('编辑项目成功')
        return ResponseUtil.success(data=edit_project_result, msg=edit_project_result.message)
    except Exception as e:
        logger.error(f'编辑项目失败: {e}')
        return ResponseUtil.error(msg=f'编辑项目失败: {str(e)}')

@projectController.delete('/{project_id}')
@ProjectAuth.require_project_access(require_manager=True)
async def delete_project(
    request: Request,
    project_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """删除项目 - 需要项目管理员权限"""
    try:
        delete_project = DeleteProjectModel(
            project_ids=str(project_id),
            update_by=current_user.user.user_name
        )
        
        delete_project_result = await ProjectsService.delete_project_services(query_db, delete_project)
        logger.info('删除项目成功')
        return ResponseUtil.success(data=delete_project_result, msg=delete_project_result.message)
    except Exception as e:
        logger.error(f'删除项目失败: {e}')
        return ResponseUtil.error(msg=f'删除项目失败: {str(e)}')

@projectController.get('/{project_id}')
@ProjectAuth.require_project_access()
async def get_project_detail(
    request: Request,
    project_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取项目详情 - 需要项目访问权限"""
    try:
        project_detail = await ProjectsService.get_project_detail_services(query_db, project_id)
        logger.info('获取项目详情成功')
        return ResponseUtil.success(data=project_detail)
    except Exception as e:
        logger.error(f'获取项目详情失败: {e}')
        return ResponseUtil.error(msg=f'获取项目详情失败: {str(e)}')

@projectController.get('/{project_id}/members')
@ProjectAuth.require_project_access()
async def get_project_members(
    request: Request,
    project_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取项目成员列表"""
    try:
        from module_business.service.project_members_service import ProjectMembersService
        members = await ProjectMembersService.get_project_members_services(query_db, project_id)
        logger.info('获取项目成员列表成功')
        return ResponseUtil.success(data=members)
    except Exception as e:
        logger.error(f'获取项目成员列表失败: {e}')
        return ResponseUtil.error(msg=f'获取项目成员列表失败: {str(e)}')

@projectController.post('/{project_id}/members')
@EnhancedAuth.require_permission(PermissionConstants.PROJECT_MEMBER_MANAGE)
@ProjectAuth.require_project_access(require_manager=True)
async def add_project_member(
    request: Request,
    project_id: int,
    member_data: dict,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """添加项目成员 - 需要项目成员管理权限"""
    try:
        from module_business.service.project_members_service import ProjectMembersService
        from module_business.entity.vo.project_members_vo import AddProjectMemberModel
        
        add_member = AddProjectMemberModel(
            project_id=project_id,
            user_id=member_data.get('user_id'),
            role_type=member_data.get('role_type', 'member'),
            create_by=current_user.user.user_name
        )
        
        result = await ProjectMembersService.add_project_member_services(query_db, add_member)
        logger.info('添加项目成员成功')
        return ResponseUtil.success(data=result, msg=result.message)
    except Exception as e:
        logger.error(f'添加项目成员失败: {e}')
        return ResponseUtil.error(msg=f'添加项目成员失败: {str(e)}')

@projectController.delete('/{project_id}/members/{user_id}')
@EnhancedAuth.require_permission(PermissionConstants.PROJECT_MEMBER_MANAGE)
@ProjectAuth.require_project_access(require_manager=True)
async def remove_project_member(
    request: Request,
    project_id: int,
    user_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """移除项目成员 - 需要项目成员管理权限"""
    try:
        from module_business.service.project_members_service import ProjectMembersService
        
        result = await ProjectMembersService.remove_project_member_services(
            query_db, project_id, user_id
        )
        logger.info('移除项目成员成功')
        return ResponseUtil.success(data=result, msg=result.message)
    except Exception as e:
        logger.error(f'移除项目成员失败: {e}')
        return ResponseUtil.error(msg=f'移除项目成员失败: {str(e)}')

@projectController.get('/check-permission/{project_id}')
async def check_project_permission(
    request: Request,
    project_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """检查项目权限 - 供前端使用"""
    try:
        from module_admin.aspect.enhanced_auth import ProjectAuthService
        
        # 检查各种权限
        can_view = await ProjectAuthService.check_project_access(
            query_db, current_user.user.user_id, project_id, require_manager=False
        )
        can_manage = await ProjectAuthService.check_project_access(
            query_db, current_user.user.user_id, project_id, require_manager=True
        )
        
        permissions = {
            'can_view': can_view,
            'can_edit': can_manage,
            'can_delete': can_manage,
            'can_manage_members': can_manage and PermissionConstants.PROJECT_MEMBER_MANAGE in current_user.permissions
        }
        
        logger.info('检查项目权限成功')
        return ResponseUtil.success(data=permissions)
    except Exception as e:
        logger.error(f'检查项目权限失败: {e}')
        return ResponseUtil.error(msg=f'检查项目权限失败: {str(e)}')