import asyncio
from config.get_db import get_db
from sqlalchemy import text

async def check():
    db_gen = get_db()
    db = await anext(db_gen)
    
    # 检查项目管理和数据库管理菜单的详细信息
    result = await db.execute(text("""
        SELECT menu_id, menu_name, path, menu_type, parent_id, visible, status, component, is_frame
        FROM sys_menu 
        WHERE menu_id IN (2069, 2070)
        ORDER BY menu_id
    """))
    menus = result.mappings().all()
    print('Menu details:')
    for menu in menus:
        print(f'  {menu["menu_id"]}: {menu["menu_name"]}')
        print(f'    path: {menu["path"]}')
        print(f'    menu_type: {menu["menu_type"]}')
        print(f'    parent_id: {menu["parent_id"]}')
        print(f'    visible: {menu["visible"]}')
        print(f'    status: {menu["status"]}')
        print(f'    component: {menu["component"]}')
        print(f'    is_frame: {menu["is_frame"]}')
        print()
    
    await db.close()

asyncio.run(check())