# Project Members 422 Error Fix

## Problem Description

The API endpoint `GET /dev-api/business/project-members/available-users?projectId=87` was returning a 422 Unprocessable Entity error.

## Root Cause Analysis

Based on the error logs and code analysis, the issue was related to permission checking in the project members service. The error logs showed "无权限访问此项目" (No permission to access this project) for the `get_project_members` method.

## Issues Identified

1. **Permission Check Logic**: The permission checking logic was too restrictive and didn't account for system administrators
2. **Error Handling**: The error handling was not providing specific enough error messages
3. **Logging**: Insufficient logging made it difficult to diagnose the root cause

## Fixes Applied

### 1. Improved Permission Checking Logic

**File**: `ai-platform-backend/module_business/service/project_members_service.py`

**Changes**:
- Enhanced `_check_project_permission` method with better error handling and logging
- Added system administrator fallback check for both access and management permissions
- Improved logging to track permission check steps

**Before**:
```python
result = await db.execute(text("""
    SELECT 1 FROM rd_projects p
    WHERE p.project_id = :project_id 
    AND p.is_deleted = 0
    AND (
        p.owner_id = :user_id
        OR EXISTS (
            SELECT 1 FROM rd_project_members pm 
            WHERE pm.project_id = p.project_id 
            AND pm.user_id = :user_id 
            AND pm.is_deleted = 0
        )
    )
"""), {"project_id": project_id, "user_id": user_id})
```

**After**:
```python
# Step-by-step permission checking with detailed logging
# 1. Check if project exists
# 2. Check if user is project owner
# 3. Check if user is project member
# 4. Check if user is system administrator (fallback)
```

### 2. Enhanced Error Handling in Controllers

**File**: `ai-platform-backend/module_business/controller/project_members_controller.py`

**Changes**:
- Added input validation for project_id and user_id
- Improved error response codes (403 for permission errors, 404 for not found, 422 for validation errors)
- Added detailed logging for debugging

**Before**:
```python
except Exception as e:
    logger.error(f"获取可添加用户失败: {str(e)}")
    return ResponseUtil.error(msg=f"获取可添加用户失败: {str(e)}", code=422)
```

**After**:
```python
except Exception as e:
    error_msg = str(e)
    logger.error(f"获取可添加用户失败: {error_msg}")
    
    # Return appropriate error codes based on error type
    if "无权限" in error_msg:
        return ResponseUtil.error(msg=error_msg, code=403)
    elif "项目不存在" in error_msg:
        return ResponseUtil.error(msg=error_msg, code=404)
    else:
        return ResponseUtil.error(msg=f"获取可添加用户失败: {error_msg}", code=422)
```

### 3. System Administrator Fallback

Added checks for system administrator role to allow admin users to access and manage all projects:

```python
# Check if user is system administrator (fallback)
admin_result = await db.execute(text("""
    SELECT 1 FROM sys_user_role ur
    JOIN sys_role r ON ur.role_id = r.role_id
    WHERE ur.user_id = :user_id 
    AND r.role_key = 'admin'
    AND ur.del_flag = '0'
    AND r.status = '0'
"""), {"user_id": user_id})
```

## Expected Results

After applying these fixes:

1. **Better Error Messages**: Users will receive more specific error messages indicating the exact reason for failure
2. **System Admin Access**: System administrators will be able to access and manage all projects
3. **Improved Logging**: Better logging will help diagnose future issues
4. **Proper HTTP Status Codes**: API will return appropriate HTTP status codes (403, 404, 422) instead of always returning 422

## Testing Recommendations

1. Test with a regular project member
2. Test with a project manager
3. Test with a project owner
4. Test with a system administrator
5. Test with invalid project IDs
6. Test with non-existent users

## Files Modified

1. `ai-platform-backend/module_business/service/project_members_service.py`
2. `ai-platform-backend/module_business/controller/project_members_controller.py`

## Deployment Notes

- These changes are backward compatible
- No database schema changes required
- No configuration changes required
- Restart the application after deploying the changes