"""
数据权限过滤器
"""
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_
from sqlalchemy.orm import Query
from module_admin.entity.vo.user_vo import CurrentUserModel
from config.role_constants import RoleConstants, PermissionConstants

class DataScopeFilter:
    """数据权限过滤器"""
    
    @staticmethod
    async def filter_projects(db: AsyncSession, current_user: CurrentUserModel, query):
        """过滤项目数据"""
        user_id = current_user.user.user_id
        
        # 超级管理员看所有数据
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return query
        
        # 项目管理员和成员只能看到自己的项目
        try:
            from module_business.entity.do.projects_do import Projects
            
            # 检查是否存在项目成员表
            try:
                from module_business.entity.do.project_members_do import ProjectMembers
                
                # 自己负责的项目或参与的项目
                return query.filter(
                    or_(
                        Projects.owner_id == user_id,
                        Projects.project_id.in_(
                            db.query(ProjectMembers.project_id).filter(
                                ProjectMembers.user_id == user_id
                            )
                        )
                    )
                )
            except:
                # 如果项目成员表不存在，只能看到自己负责的项目
                return query.filter(Projects.owner_id == user_id)
                
        except Exception:
            # 如果出现异常，返回空结果
            return query.filter(False)
    
    @staticmethod
    async def filter_tasks(db: AsyncSession, current_user: CurrentUserModel, query):
        """过滤任务数据"""
        user_id = current_user.user.user_id
        
        # 超级管理员看所有数据
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return query
        
        try:
            from module_business.entity.do.tasks_do import Tasks
            from module_business.entity.do.projects_do import Projects
            
            # 普通用户无法看到任务
            if RoleConstants.NORMAL_USER in current_user.roles:
                return query.filter(False)
            
            try:
                from module_business.entity.do.project_members_do import ProjectMembers
                
                # 只能看到自己项目下的任务
                accessible_projects_subquery = db.query(Projects.project_id).filter(
                    or_(
                        Projects.owner_id == user_id,
                        Projects.project_id.in_(
                            db.query(ProjectMembers.project_id).filter(
                                ProjectMembers.user_id == user_id
                            )
                        )
                    )
                )
                
                return query.filter(Tasks.project_id.in_(accessible_projects_subquery))
            except:
                # 如果项目成员表不存在，只能看到自己负责项目下的任务
                accessible_projects_subquery = db.query(Projects.project_id).filter(
                    Projects.owner_id == user_id
                )
                return query.filter(Tasks.project_id.in_(accessible_projects_subquery))
                
        except Exception:
            return query.filter(False)
    
    @staticmethod
    async def filter_databases(db: AsyncSession, current_user: CurrentUserModel, query):
        """过滤数据库数据"""
        user_id = current_user.user.user_id
        
        # 超级管理员看所有数据
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return query
        
        # 项目成员无数据库访问权限
        if RoleConstants.PROJECT_MEMBER in current_user.roles:
            return query.filter(False)
        
        # 普通用户无数据库访问权限
        if RoleConstants.NORMAL_USER in current_user.roles:
            return query.filter(False)
        
        try:
            from module_business.entity.do.knowledge_bases_do import KnowledgeBases
            from module_business.entity.do.projects_do import Projects
            
            try:
                from module_business.entity.do.project_members_do import ProjectMembers
                
                # 项目管理员只能看到自己管理项目相关的数据库
                accessible_projects_subquery = db.query(Projects.project_id).filter(
                    or_(
                        Projects.owner_id == user_id,
                        Projects.project_id.in_(
                            db.query(ProjectMembers.project_id).filter(
                                and_(
                                    ProjectMembers.user_id == user_id,
                                    ProjectMembers.role_type == 'manager'
                                )
                            )
                        )
                    )
                )
                
                return query.filter(KnowledgeBases.project_id.in_(accessible_projects_subquery))
            except:
                # 如果项目成员表不存在，只能看到自己负责项目的数据库
                accessible_projects_subquery = db.query(Projects.project_id).filter(
                    Projects.owner_id == user_id
                )
                return query.filter(KnowledgeBases.project_id.in_(accessible_projects_subquery))
                
        except Exception:
            return query.filter(False)
    
    @staticmethod
    async def filter_files(db: AsyncSession, current_user: CurrentUserModel, query):
        """过滤文件数据"""
        user_id = current_user.user.user_id
        
        # 超级管理员看所有数据
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return query
        
        # 基于知识库权限过滤文件
        try:
            from module_business.entity.do.files_do import Files
            from module_business.entity.do.knowledge_bases_do import KnowledgeBases
            from module_business.entity.do.projects_do import Projects
            
            try:
                from module_business.entity.do.project_members_do import ProjectMembers
                
                # 通过知识库关联项目来过滤文件
                accessible_kb_subquery = db.query(KnowledgeBases.kb_id).join(
                    Projects, KnowledgeBases.project_id == Projects.project_id
                ).filter(
                    or_(
                        Projects.owner_id == user_id,
                        Projects.project_id.in_(
                            db.query(ProjectMembers.project_id).filter(
                                ProjectMembers.user_id == user_id
                            )
                        )
                    )
                )
                
                return query.filter(Files.kb_id.in_(accessible_kb_subquery))
            except:
                # 如果项目成员表不存在
                accessible_kb_subquery = db.query(KnowledgeBases.kb_id).join(
                    Projects, KnowledgeBases.project_id == Projects.project_id
                ).filter(Projects.owner_id == user_id)
                
                return query.filter(Files.kb_id.in_(accessible_kb_subquery))
                
        except Exception:
            return query.filter(False)
    
    @staticmethod
    def check_can_edit_task(current_user: CurrentUserModel, task_creator_id: int) -> bool:
        """检查是否可以编辑任务"""
        # 超级管理员可以编辑所有任务
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return True
        
        # 任务创建者可以编辑自己的任务
        if task_creator_id == current_user.user.user_id:
            return True
        
        # 项目管理员可以编辑所有任务（需要在调用处验证项目权限）
        if (PermissionConstants.TASK_EDIT_ALL in current_user.permissions and 
            RoleConstants.PROJECT_MANAGER in current_user.roles):
            return True
        
        return False
    
    @staticmethod
    def check_can_delete_task(current_user: CurrentUserModel, task_creator_id: int) -> bool:
        """检查是否可以删除任务"""
        # 超级管理员可以删除所有任务
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return True
        
        # 任务创建者可以删除自己的任务
        if task_creator_id == current_user.user.user_id:
            return True
        
        # 项目管理员可以删除所有任务（需要在调用处验证项目权限）
        if (PermissionConstants.TASK_DELETE_ALL in current_user.permissions and 
            RoleConstants.PROJECT_MANAGER in current_user.roles):
            return True
        
        return False
    
    @staticmethod
    def check_menu_access(current_user: CurrentUserModel, menu_path: str) -> bool:
        """检查菜单访问权限"""
        # 超级管理员可以访问所有菜单
        if PermissionConstants.ALL_PERMISSION in current_user.permissions:
            return True
        
        # 根据路径映射菜单权限
        menu_permission_map = {
            '/index': PermissionConstants.MENU_HOME,
            '/home': PermissionConstants.MENU_HOME,
            '/tools': PermissionConstants.MENU_TOOLS,
            '/projects': PermissionConstants.MENU_PROJECT,
            '/knowledge-bases': PermissionConstants.MENU_DATABASE,
            '/database': PermissionConstants.MENU_DATABASE
        }
        
        for route, permission in menu_permission_map.items():
            if menu_path.startswith(route):
                return permission in current_user.permissions
        
        # 默认允许访问（兜底策略）
        return True

class DataScopeHelper:
    """数据权限辅助类"""
    
    @staticmethod
    def get_user_accessible_project_ids(db: AsyncSession, user_id: int) -> list:
        """获取用户可访问的项目ID列表"""
        try:
            from module_business.entity.do.projects_do import Projects
            
            project_ids = []
            
            # 获取用户负责的项目
            owned_projects = db.query(Projects.project_id).filter(Projects.owner_id == user_id).all()
            project_ids.extend([p.project_id for p in owned_projects])
            
            # 获取用户参与的项目
            try:
                from module_business.entity.do.project_members_do import ProjectMembers
                member_projects = db.query(ProjectMembers.project_id).filter(
                    ProjectMembers.user_id == user_id
                ).all()
                project_ids.extend([p.project_id for p in member_projects])
            except:
                pass
            
            return list(set(project_ids))  # 去重
        except Exception:
            return []
    
    @staticmethod
    def is_project_manager(db: AsyncSession, user_id: int, project_id: int) -> bool:
        """检查用户是否是项目管理员"""
        try:
            from module_business.entity.do.projects_do import Projects
            
            # 检查是否是项目负责人
            project = db.query(Projects).filter(
                and_(Projects.project_id == project_id, Projects.owner_id == user_id)
            ).first()
            if project:
                return True
            
            # 检查是否是项目管理员成员
            try:
                from module_business.entity.do.project_members_do import ProjectMembers
                member = db.query(ProjectMembers).filter(
                    and_(
                        ProjectMembers.project_id == project_id,
                        ProjectMembers.user_id == user_id,
                        ProjectMembers.role_type == 'manager'
                    )
                ).first()
                return member is not None
            except:
                return False
                
        except Exception:
            return False