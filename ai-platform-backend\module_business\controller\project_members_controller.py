from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.project_members_service import ProjectMembersService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from typing import List

projectMembersController = APIRouter(prefix='/business/project-members', dependencies=[Depends(LoginService.get_current_user)])


@projectMembersController.get('/list/{project_id}')
async def get_project_members(
    request: Request,
    project_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取项目成员列表
    """
    try:
        logger.info(f"获取项目成员列表，项目ID: {project_id}, 当前用户ID: {current_user.user.user_id}")
        
        # 验证项目ID
        if not project_id or project_id <= 0:
            logger.error(f"无效的项目ID: {project_id}")
            return ResponseUtil.error(msg="无效的项目ID", code=422)
        
        # 验证用户ID
        if not current_user or not current_user.user or not current_user.user.user_id:
            logger.error("当前用户信息无效")
            return ResponseUtil.error(msg="用户信息无效", code=401)
        
        members = await ProjectMembersService.get_project_members(
            query_db, 
            project_id, 
            current_user.user.user_id
        )
        logger.info(f"成功获取项目成员列表，成员数量: {len(members)}")
        return ResponseUtil.success(data=members)
    except Exception as e:
        error_msg = str(e)
        logger.error(f"获取项目成员失败: {error_msg}")
        
        # 根据错误类型返回不同的错误码
        if "无权限" in error_msg:
            return ResponseUtil.error(msg=error_msg, code=403)
        elif "项目不存在" in error_msg:
            return ResponseUtil.error(msg=error_msg, code=404)
        else:
            return ResponseUtil.error(msg=f"获取项目成员失败: {error_msg}", code=422)


@projectMembersController.get('/available-users')
async def get_available_users(
    request: Request,
    project_id: int = Query(..., description="项目ID"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取可添加的用户列表
    """
    try:
        logger.info(f"获取可添加用户列表，项目ID: {project_id}, 当前用户ID: {current_user.user.user_id}")
        
        # 验证项目ID
        if not project_id or project_id <= 0:
            logger.error(f"无效的项目ID: {project_id}")
            return ResponseUtil.error(msg="无效的项目ID", code=422)
        
        # 验证用户ID
        if not current_user or not current_user.user or not current_user.user.user_id:
            logger.error("当前用户信息无效")
            return ResponseUtil.error(msg="用户信息无效", code=401)
        
        users = await ProjectMembersService.get_available_users(
            query_db, 
            project_id, 
            current_user.user.user_id
        )
        logger.info(f"成功获取可添加用户列表，用户数量: {len(users)}")
        return ResponseUtil.success(data=users)
    except Exception as e:
        error_msg = str(e)
        logger.error(f"获取可添加用户失败: {error_msg}")
        
        # 根据错误类型返回不同的错误码
        if "无权限" in error_msg:
            return ResponseUtil.error(msg=error_msg, code=403)
        elif "项目不存在" in error_msg:
            return ResponseUtil.error(msg=error_msg, code=404)
        else:
            return ResponseUtil.error(msg=f"获取可添加用户失败: {error_msg}", code=422)


@projectMembersController.post('/add')
@Log(title='项目成员管理', business_type=BusinessType.INSERT)
async def add_project_member(
    request: Request,
    project_id: int = Query(..., description="项目ID"),
    user_id: int = Query(..., description="用户ID"),
    role_type: str = Query('member', description="角色类型"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    添加项目成员
    """
    try:
        success, message = await ProjectMembersService.add_project_member(
            query_db, 
            project_id, 
            user_id, 
            role_type, 
            current_user.user.user_id
        )
        
        if success:
            await query_db.commit()
            return ResponseUtil.success(msg=message)
        else:
            await query_db.rollback()
            return ResponseUtil.error(msg=message)
    except Exception as e:
        await query_db.rollback()
        logger.error(f"添加项目成员失败: {str(e)}")
        return ResponseUtil.error(msg="添加项目成员失败")


@projectMembersController.put('/update-role')
@Log(title='项目成员管理', business_type=BusinessType.UPDATE)
async def update_member_role(
    request: Request,
    project_id: int = Query(..., description="项目ID"),
    user_id: int = Query(..., description="用户ID"),
    role_type: str = Query(..., description="新角色类型"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新成员角色
    """
    try:
        success, message = await ProjectMembersService.update_member_role(
            query_db, 
            project_id, 
            user_id, 
            role_type, 
            current_user.user.user_id
        )
        
        if success:
            await query_db.commit()
            return ResponseUtil.success(msg=message)
        else:
            await query_db.rollback()
            return ResponseUtil.error(msg=message)
    except Exception as e:
        await query_db.rollback()
        logger.error(f"更新成员角色失败: {str(e)}")
        return ResponseUtil.error(msg="更新成员角色失败")


@projectMembersController.delete('/remove')
@Log(title='项目成员管理', business_type=BusinessType.DELETE)
async def remove_project_member(
    request: Request,
    project_id: int = Query(..., description="项目ID"),
    user_id: int = Query(..., description="用户ID"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    移除项目成员
    """
    try:
        success, message = await ProjectMembersService.remove_project_member(
            query_db, 
            project_id, 
            user_id, 
            current_user.user.user_id
        )
        
        if success:
            await query_db.commit()
            return ResponseUtil.success(msg=message)
        else:
            await query_db.rollback()
            return ResponseUtil.error(msg=message)
    except Exception as e:
        await query_db.rollback()
        logger.error(f"移除项目成员失败: {str(e)}")
        return ResponseUtil.error(msg="移除项目成员失败")