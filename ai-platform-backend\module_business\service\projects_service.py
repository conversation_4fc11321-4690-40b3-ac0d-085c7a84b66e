from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.projects_dao import ProjectsDao
from module_business.entity.vo.projects_vo import DeleteProjectsModel, ProjectsModel, ProjectsPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.log_util import logger


class ProjectsService:
    """
    项目信息管理模块服务层
    """

    @classmethod
    async def get_projects_list_services(
        cls, query_db: AsyncSession, query_object: ProjectsPageQueryModel, is_page: bool = False, current_user_id: int = None
    ):
        """
        获取项目信息管理列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :param current_user_id: 当前用户ID，用于权限过滤
        :return: 项目信息管理列表信息对象
        """
        projects_list_result = await ProjectsDao.get_projects_list(query_db, query_object, is_page, current_user_id)

        return projects_list_result


    @classmethod
    async def add_projects_services(cls, query_db: AsyncSession, page_object: ProjectsModel):
        """
        新增项目信息管理信息service

        :param query_db: orm对象
        :param page_object: 新增项目信息管理对象
        :return: 新增项目信息管理校验结果
        """
        db_projects = await ProjectsDao.add_projects_dao(query_db, page_object)
        return CrudResponseModel(is_success=True, message='新增成功', result=db_projects)

    @classmethod
    async def edit_projects_services(cls, query_db: AsyncSession, page_object: ProjectsModel):
        """
        编辑项目信息管理信息service

        :param query_db: orm对象
        :param page_object: 编辑项目信息管理对象
        :return: 编辑项目信息管理校验结果
        """
        edit_projects = page_object.model_dump(exclude_unset=True, exclude={})
        projects_info = await cls.projects_detail_services(query_db, page_object.project_id)
        if projects_info.project_id:
            try:
                # 检查是否为逻辑删除操作
                if page_object.is_deleted == 1:
                    # 同时删除关联的数据库
                    from module_business.service.knowledge_bases_service import KnowledgeBasesService
                    
                    # 先查询要删除的数据库
                    kb_list = await KnowledgeBasesService.get_knowledge_bases_by_project_id_services(
                        query_db, page_object.project_id
                    )
                    
                    if kb_list:
                        logger.info(f'项目 {page_object.project_id} 即将删除 {len(kb_list)} 个关联数据库')
                        for kb in kb_list:
                            logger.info(f'将删除数据库: {kb.kb_name} (ID: {kb.kb_id})')
                    
                    # 删除关联的数据库
                    await KnowledgeBasesService.delete_knowledge_bases_by_project_id_services(
                        query_db, page_object.project_id
                    )
                    
                    logger.info(f'项目 {page_object.project_id} 关联数据库删除完成')
                
                await ProjectsDao.edit_projects_dao(query_db, edit_projects)
                await query_db.commit()
                
                if page_object.is_deleted == 1:
                    return CrudResponseModel(is_success=True, message='项目及关联数据库删除成功')
                else:
                    return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='项目信息管理不存在')

    @classmethod
    async def delete_projects_services(cls, query_db: AsyncSession, page_object: DeleteProjectsModel):
        """
        删除项目信息管理信息service

        :param query_db: orm对象
        :param page_object: 删除项目信息管理对象
        :return: 删除项目信息管理校验结果
        """
        await ProjectsDao.delete_projects_dao(query_db, page_object)
        return CrudResponseModel(is_success=True, message='删除成功')

    @classmethod
    async def projects_detail_services(cls, query_db: AsyncSession, project_id: int):
        """
        获取项目信息管理详细信息service

        :param query_db: orm对象
        :param project_id: 项目信息管理ID
        :return: 项目信息管理详细信息对象
        """
        projects_info = await ProjectsDao.get_projects_detail_by_id(query_db, project_id)
        return projects_info

    @staticmethod
    async def export_projects_list_services(projects_list: List):
        """
        导出项目信息管理列表信息service

        :param projects_list: 项目信息管理列表信息
        :return: 导出项目信息管理列表信息对象
        """
        # 转换数据格式
        export_data = []
        for projects in projects_list:
            export_data.append({
                '项目名称': projects.project_name,
                '项目描述': projects.description,
                '所有者ID': projects.owner_id,
                '创建时间': projects.created_at.strftime('%Y-%m-%d %H:%M:%S') if projects.created_at else '',
                '更新时间': projects.updated_at.strftime('%Y-%m-%d %H:%M:%S') if projects.updated_at else '',
            })

        # 导出Excel
        excel_data = ExcelUtil.export_excel(export_data, '项目信息管理列表')
        return excel_data
