-- 项目权限管理系统初始化SQL

-- 1. 创建项目成员表
CREATE TABLE IF NOT EXISTS rd_project_members (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_type ENUM('manager', 'member') NOT NULL DEFAULT 'member' COMMENT '项目角色类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_project_user (project_id, user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_role_type (role_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员关联表';

-- 2. 新增角色
-- 项目管理员角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES ('项目管理员', 'project_manager', 3, '7', 1, 1, '0', '0', 'admin', NOW(), '项目管理员角色')
ON DUPLICATE KEY UPDATE role_name='项目管理员';

-- 项目成员角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES ('项目成员', 'project_member', 4, '8', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色')
ON DUPLICATE KEY UPDATE role_name='项目成员';

-- 3. 新增菜单权限
-- 项目管理菜单 (假设父级菜单ID为100，需要根据实际情况调整)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
('项目管理', 0, 6, 'business/projects', 'business/projects/index', '', 'Projects', 1, 0, 'C', '0', '0', 'business:projects:list', 'tree', 'admin', NOW(), '项目管理菜单'),
('项目列表', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:projects:list' LIMIT 1) t), 1, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:list', '#', 'admin', NOW(), ''),
('项目新增', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:projects:list' LIMIT 1) t), 2, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:add', '#', 'admin', NOW(), ''),
('项目修改', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:projects:list' LIMIT 1) t), 3, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:edit', '#', 'admin', NOW(), ''),
('项目删除', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:projects:list' LIMIT 1) t), 4, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:delete', '#', 'admin', NOW(), ''),
('成员管理', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:projects:list' LIMIT 1) t), 5, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:member:manage', '#', 'admin', NOW(), ''),

('任务管理', 0, 7, 'business/tasks', 'business/tasks/index', '', 'Tasks', 1, 0, 'C', '0', '0', 'business:tasks:list', 'cascader', 'admin', NOW(), '任务管理菜单'),
('任务列表', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 1, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:list', '#', 'admin', NOW(), ''),
('任务新增', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 2, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:add', '#', 'admin', NOW(), ''),
('任务修改(全部)', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 3, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:edit:all', '#', 'admin', NOW(), ''),
('任务修改(自己)', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 4, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:edit:own', '#', 'admin', NOW(), ''),
('任务删除(全部)', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 5, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:delete:all', '#', 'admin', NOW(), ''),
('任务删除(自己)', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 6, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:delete:own', '#', 'admin', NOW(), ''),
('项目任务查看', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:tasks:list' LIMIT 1) t), 7, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:view:project', '#', 'admin', NOW(), ''),

('数据库管理', 0, 8, 'business/knowledge-bases', 'business/knowledge-bases/index', '', 'Database', 1, 0, 'C', '0', '0', 'business:knowledge-bases:list', 'database', 'admin', NOW(), '数据库管理菜单'),
('数据库列表', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:knowledge-bases:list' LIMIT 1) t), 1, '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:list', '#', 'admin', NOW(), ''),
('数据库管理', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'business:knowledge-bases:list' LIMIT 1) t), 2, '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:manage', '#', 'admin', NOW(), ''),

('工具广场', 0, 9, 'tools', 'tools/index', '', 'Tools', 1, 0, 'C', '0', '0', 'menu:tools', 'tool', 'admin', NOW(), '工具广场菜单')
ON DUPLICATE KEY UPDATE menu_name=VALUES(menu_name);

-- 4. 角色菜单权限分配
-- 获取角色ID
SET @project_manager_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_manager' LIMIT 1);
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' LIMIT 1);
SET @common_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'common' LIMIT 1);

-- 项目管理员权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @project_manager_role_id, menu_id FROM sys_menu WHERE perms IN (
    'business:projects:list',
    'business:projects:add', 
    'business:projects:edit',
    'business:projects:delete',
    'business:projects:member:manage',
    'business:tasks:list',
    'business:tasks:add',
    'business:tasks:edit:all',
    'business:tasks:delete:all',
    'business:tasks:view:project',
    'business:knowledge-bases:list',
    'business:knowledge-bases:manage',
    'menu:tools'
) AND @project_manager_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 项目成员权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @project_member_role_id, menu_id FROM sys_menu WHERE perms IN (
    'business:projects:list',
    'business:tasks:list',
    'business:tasks:add',
    'business:tasks:edit:own',
    'business:tasks:delete:own',
    'business:tasks:view:project',
    'menu:tools'
) AND @project_member_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 普通用户权限(只保留工具广场)
DELETE FROM sys_role_menu WHERE role_id = @common_role_id AND @common_role_id IS NOT NULL;
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @common_role_id, menu_id FROM sys_menu WHERE perms = 'menu:tools' AND @common_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 5. 更新现有角色的数据权限范围
-- 项目管理员：管理的项目数据 (data_scope = '7')
UPDATE sys_role SET data_scope = '7' WHERE role_key = 'project_manager';

-- 项目成员：参与的项目数据 (data_scope = '8') 
UPDATE sys_role SET data_scope = '8' WHERE role_key = 'project_member';

-- 普通用户：无项目数据权限 (data_scope = '9')
UPDATE sys_role SET data_scope = '9' WHERE role_key = 'common';

-- 6. 示例：为现有用户分配角色(可选，根据实际需要调整)
-- 将用户ID为2的用户设为项目管理员
-- INSERT INTO sys_user_role (user_id, role_id) VALUES (2, @project_manager_role_id) ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 7. 创建示例项目成员数据(可选)
-- INSERT INTO rd_project_members (project_id, user_id, role_type, created_at, updated_at) 
-- VALUES (1, 2, 'manager', NOW(), NOW()) ON DUPLICATE KEY UPDATE role_type=VALUES(role_type);

-- 更新角色表数据
-- 保留现有的超级管理员(1)和普通角色(2)，新增项目管理员、项目成员、普通用户角色

-- 项目管理员角色
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES (3, '项目管理员', 'project_manager', 3, '2', 1, 1, '0', '0', 'admin', current_timestamp, '管理负责的项目和团队成员');

-- 项目成员角色
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES (4, '项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', current_timestamp, '项目组成员，参与项目任务');

-- 普通用户角色
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES (5, '普通用户', 'normal_user', 5, '5', 1, 1, '0', '0', 'admin', current_timestamp, '普通用户，仅能访问基础功能');

-- 插入菜单权限数据
-- 菜单级权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('首页权限', 0, 1, '', '', 'F', '0', '0', 'menu:home', '#', 'admin', current_timestamp, '首页菜单访问权限'),
('工具广场权限', 0, 2, '', '', 'F', '0', '0', 'menu:tools', '#', 'admin', current_timestamp, '工具广场菜单访问权限'),
('项目管理权限', 0, 3, '', '', 'F', '0', '0', 'menu:project', '#', 'admin', current_timestamp, '项目管理菜单访问权限'),
('数据库管理权限', 0, 4, '', '', 'F', '0', '0', 'menu:database', '#', 'admin', current_timestamp, '数据库管理菜单访问权限');

-- 项目管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('项目列表', 0, 10, '', '', 'F', '0', '0', 'project:list', '#', 'admin', current_timestamp, '查看项目列表权限'),
('项目创建', 0, 11, '', '', 'F', '0', '0', 'project:create', '#', 'admin', current_timestamp, '创建项目权限'),
('项目编辑', 0, 12, '', '', 'F', '0', '0', 'project:edit', '#', 'admin', current_timestamp, '编辑项目权限'),
('项目删除', 0, 13, '', '', 'F', '0', '0', 'project:delete', '#', 'admin', current_timestamp, '删除项目权限'),
('项目成员管理', 0, 14, '', '', 'F', '0', '0', 'project:member:manage', '#', 'admin', current_timestamp, '管理项目成员权限');

-- 任务管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('任务列表', 0, 20, '', '', 'F', '0', '0', 'task:list', '#', 'admin', current_timestamp, '查看任务列表权限'),
('任务创建', 0, 21, '', '', 'F', '0', '0', 'task:create', '#', 'admin', current_timestamp, '创建任务权限'),
('编辑自己任务', 0, 22, '', '', 'F', '0', '0', 'task:edit:own', '#', 'admin', current_timestamp, '编辑自己任务权限'),
('编辑所有任务', 0, 23, '', '', 'F', '0', '0', 'task:edit:all', '#', 'admin', current_timestamp, '编辑所有任务权限'),
('删除自己任务', 0, 24, '', '', 'F', '0', '0', 'task:delete:own', '#', 'admin', current_timestamp, '删除自己任务权限'),
('删除所有任务', 0, 25, '', '', 'F', '0', '0', 'task:delete:all', '#', 'admin', current_timestamp, '删除所有任务权限'),
('任务执行', 0, 26, '', '', 'F', '0', '0', 'task:execute', '#', 'admin', current_timestamp, '执行任务权限'),
('任务下载', 0, 27, '', '', 'F', '0', '0', 'task:download', '#', 'admin', current_timestamp, '下载任务结果权限');

-- 数据库管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('数据库列表', 0, 30, '', '', 'F', '0', '0', 'database:list', '#', 'admin', current_timestamp, '查看数据库列表权限'),
('数据库创建', 0, 31, '', '', 'F', '0', '0', 'database:create', '#', 'admin', current_timestamp, '创建数据库权限'),
('数据库编辑', 0, 32, '', '', 'F', '0', '0', 'database:edit', '#', 'admin', current_timestamp, '编辑数据库权限'),
('数据库删除', 0, 33, '', '', 'F', '0', '0', 'database:delete', '#', 'admin', current_timestamp, '删除数据库权限');

-- 为项目管理员分配权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 3, menu_id FROM sys_menu WHERE perms IN (
    'menu:home', 'menu:tools', 'menu:project', 'menu:database',
    'project:list', 'project:create', 'project:edit', 'project:delete', 'project:member:manage',
    'task:list', 'task:create', 'task:edit:all', 'task:delete:all', 'task:execute', 'task:download',
    'database:list', 'database:create', 'database:edit', 'database:delete'
);

-- 为项目成员分配权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 4, menu_id FROM sys_menu WHERE perms IN (
    'menu:home', 'menu:tools', 'menu:project',
    'project:list',
    'task:list', 'task:create', 'task:edit:own', 'task:delete:own', 'task:execute', 'task:download'
);

-- 为普通用户分配权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 5, menu_id FROM sys_menu WHERE perms IN (
    'menu:home', 'menu:tools'
);

-- 创建项目成员表（如果不存在）
CREATE TABLE IF NOT EXISTS project_members (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role_type VARCHAR(20) NOT NULL DEFAULT 'member', -- 'manager', 'member'
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(64),
    update_time TIMESTAMP,
    update_by VARCHAR(64),
    UNIQUE(project_id, user_id)
);

COMMENT ON TABLE project_members IS '项目成员表';
COMMENT ON COLUMN project_members.project_id IS '项目ID';
COMMENT ON COLUMN project_members.user_id IS '用户ID';
COMMENT ON COLUMN project_members.role_type IS '成员角色类型：manager-项目管理员，member-项目成员';

COMMIT;