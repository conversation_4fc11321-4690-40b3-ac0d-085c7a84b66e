"""
任务管理控制器 - 应用新的权限控制
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.aspect.enhanced_auth import EnhancedAuth, TaskAuth
from module_admin.aspect.data_scope_enhanced import DataScopeFilter
from module_business.entity.vo.tasks_vo import (
    TaskPageQueryModel, AddTaskModel, EditTaskModel, DeleteTaskModel
)
from module_business.service.tasks_service import TasksService
from utils.response_util import ResponseUtil
from utils.log_util import logger
from config.role_constants import PermissionConstants

taskController = APIRouter()

@taskController.get('/list')
@EnhancedAuth.require_permission(PermissionConstants.TASK_LIST)
async def get_task_list(
    request: Request,
    query_params: TaskPageQueryModel = Depends(TaskPageQueryModel.as_query()),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取任务列表 - 应用数据权限过滤"""
    try:
        # 应用数据权限过滤
        tasks_result = await TasksService.get_task_list_services(
            query_db, query_params, current_user
        )
        logger.info('获取任务列表成功')
        return ResponseUtil.success(data=tasks_result)
    except Exception as e:
        logger.error(f'获取任务列表失败: {e}')
        return ResponseUtil.error(msg=f'获取任务列表失败: {str(e)}')

@taskController.post('/add')
@EnhancedAuth.require_permission(PermissionConstants.TASK_CREATE)
async def add_task(
    request: Request,
    add_task: AddTaskModel,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """新增任务"""
    try:
        # 检查项目访问权限
        from module_admin.aspect.enhanced_auth import ProjectAuthService
        if add_task.project_id:
            has_access = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, add_task.project_id, require_manager=False
            )
            if not has_access:
                return ResponseUtil.error(msg='无项目访问权限')
        
        # 设置创建者
        add_task.create_by = current_user.user.user_name
        add_task.create_by_id = current_user.user.user_id
        
        add_task_result = await TasksService.add_task_services(query_db, add_task)
        logger.info('新增任务成功')
        return ResponseUtil.success(data=add_task_result, msg=add_task_result.message)
    except Exception as e:
        logger.error(f'新增任务失败: {e}')
        return ResponseUtil.error(msg=f'新增任务失败: {str(e)}')

@taskController.put('/{task_id}')
@TaskAuth.require_task_access('edit')
async def edit_task(
    request: Request,
    task_id: int,
    edit_task: EditTaskModel,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """编辑任务 - 需要任务编辑权限"""
    try:
        # 检查具体的编辑权限
        from module_business.dao.tasks_dao import TasksDao
        task = await TasksDao.get_tasks_detail_by_id(query_db, task_id)
        if not task:
            return ResponseUtil.error(msg='任务不存在')
        
        # 检查是否可以编辑
        can_edit = DataScopeFilter.check_can_edit_task(current_user, task.create_by_id)
        if not can_edit:
            # 如果不是任务创建者，检查是否有项目管理员权限
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            can_manage = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, task.project_id, require_manager=True
            )
            if not can_manage:
                return ResponseUtil.error(msg='无任务编辑权限')
        
        edit_task.task_id = task_id
        edit_task.update_by = current_user.user.user_name
        
        edit_task_result = await TasksService.edit_task_services(query_db, edit_task)
        logger.info('编辑任务成功')
        return ResponseUtil.success(data=edit_task_result, msg=edit_task_result.message)
    except Exception as e:
        logger.error(f'编辑任务失败: {e}')
        return ResponseUtil.error(msg=f'编辑任务失败: {str(e)}')

@taskController.delete('/{task_id}')
@TaskAuth.require_task_access('delete')
async def delete_task(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """删除任务 - 需要任务删除权限"""
    try:
        # 检查具体的删除权限
        from module_business.dao.tasks_dao import TasksDao
        task = await TasksDao.get_tasks_detail_by_id(query_db, task_id)
        if not task:
            return ResponseUtil.error(msg='任务不存在')
        
        # 检查是否可以删除
        can_delete = DataScopeFilter.check_can_delete_task(current_user, task.create_by_id)
        if not can_delete:
            # 如果不是任务创建者，检查是否有项目管理员权限
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            can_manage = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, task.project_id, require_manager=True
            )
            if not can_manage:
                return ResponseUtil.error(msg='无任务删除权限')
        
        delete_task = DeleteTaskModel(
            task_ids=str(task_id),
            update_by=current_user.user.user_name
        )
        
        delete_task_result = await TasksService.delete_task_services(query_db, delete_task)
        logger.info('删除任务成功')
        return ResponseUtil.success(data=delete_task_result, msg=delete_task_result.message)
    except Exception as e:
        logger.error(f'删除任务失败: {e}')
        return ResponseUtil.error(msg=f'删除任务失败: {str(e)}')

@taskController.get('/{task_id}')
@TaskAuth.require_task_access('view')
async def get_task_detail(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取任务详情 - 需要任务查看权限"""
    try:
        task_detail = await TasksService.get_task_detail_services(query_db, task_id)
        logger.info('获取任务详情成功')
        return ResponseUtil.success(data=task_detail)
    except Exception as e:
        logger.error(f'获取任务详情失败: {e}')
        return ResponseUtil.error(msg=f'获取任务详情失败: {str(e)}')

@taskController.post('/{task_id}/execute')
@EnhancedAuth.require_permission(PermissionConstants.TASK_EXECUTE)
@TaskAuth.require_task_access('execute')
async def execute_task(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """执行任务 - 需要任务执行权限"""
    try:
        # 执行任务逻辑
        execute_result = await TasksService.execute_task_services(query_db, task_id, current_user)
        logger.info('执行任务成功')
        return ResponseUtil.success(data=execute_result, msg='任务执行成功')
    except Exception as e:
        logger.error(f'执行任务失败: {e}')
        return ResponseUtil.error(msg=f'执行任务失败: {str(e)}')

@taskController.get('/{task_id}/download')
@EnhancedAuth.require_permission(PermissionConstants.TASK_DOWNLOAD)
@TaskAuth.require_task_access('download')
async def download_task_result(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """下载任务结果 - 需要任务下载权限"""
    try:
        # 下载任务结果逻辑
        download_result = await TasksService.download_task_result_services(query_db, task_id)
        logger.info('下载任务结果成功')
        return ResponseUtil.success(data=download_result)
    except Exception as e:
        logger.error(f'下载任务结果失败: {e}')
        return ResponseUtil.error(msg=f'下载任务结果失败: {str(e)}')

@taskController.get('/project/{project_id}')
@EnhancedAuth.require_permission(PermissionConstants.TASK_LIST)
async def get_project_tasks(
    request: Request,
    project_id: int,
    query_params: TaskPageQueryModel = Depends(TaskPageQueryModel.as_query()),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取项目任务列表"""
    try:
        # 检查项目访问权限
        from module_admin.aspect.enhanced_auth import ProjectAuthService
        has_access = await ProjectAuthService.check_project_access(
            query_db, current_user.user.user_id, project_id, require_manager=False
        )
        if not has_access:
            return ResponseUtil.error(msg='无项目访问权限')
        
        # 设置项目ID过滤
        query_params.project_id = project_id
        tasks_result = await TasksService.get_task_list_services(
            query_db, query_params, current_user
        )
        logger.info('获取项目任务列表成功')
        return ResponseUtil.success(data=tasks_result)
    except Exception as e:
        logger.error(f'获取项目任务列表失败: {e}')
        return ResponseUtil.error(msg=f'获取项目任务列表失败: {str(e)}')

@taskController.get('/check-permission/{task_id}')
async def check_task_permission(
    request: Request,
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """检查任务权限 - 供前端使用"""
    try:
        from module_business.dao.tasks_dao import TasksDao
        task = await TasksDao.get_tasks_detail_by_id(query_db, task_id)
        if not task:
            return ResponseUtil.error(msg='任务不存在')
        
        # 检查各种权限
        can_view = await TaskAuthService.check_task_access(
            query_db, current_user.user.user_id, task_id, 'view'
        )
        can_edit = DataScopeFilter.check_can_edit_task(current_user, task.create_by_id)
        can_delete = DataScopeFilter.check_can_delete_task(current_user, task.create_by_id)
        
        # 如果不是创建者，检查项目管理员权限
        if not can_edit or not can_delete:
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            can_manage = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, task.project_id, require_manager=True
            )
            if can_manage:
                can_edit = True
                can_delete = True
        
        permissions = {
            'can_view': can_view,
            'can_edit': can_edit,
            'can_delete': can_delete,
            'can_execute': PermissionConstants.TASK_EXECUTE in current_user.permissions and can_view,
            'can_download': PermissionConstants.TASK_DOWNLOAD in current_user.permissions and can_view,
            'is_creator': task.create_by_id == current_user.user.user_id
        }
        
        logger.info('检查任务权限成功')
        return ResponseUtil.success(data=permissions)
    except Exception as e:
        logger.error(f'检查任务权限失败: {e}')
        return ResponseUtil.error(msg=f'检查任务权限失败: {str(e)}')