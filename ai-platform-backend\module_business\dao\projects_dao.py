from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.projects_do import RdProjects
from module_business.entity.vo.projects_vo import ProjectsModel, ProjectsPageQueryModel
from utils.page_util import PageUtil

class ProjectsDao:
    """
    项目信息管理模块数据库操作层
    """

    @classmethod
    async def get_projects_detail_by_id(cls, db: AsyncSession, project_id: int):
        """
        根据获取项目信息管理详细信息

        :param db: orm对象
        :param project_id: 
        :return: 项目信息管理信息对象
        """
        projects_info = (
            (
                await db.execute(
                    select(RdProjects)
                    .where(
                        RdProjects.project_id == project_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return projects_info

    @classmethod
    async def get_projects_detail_by_info(cls, db: AsyncSession, projects: ProjectsModel):
        """
        根据项目信息管理参数获取项目信息管理信息

        :param db: orm对象
        :param projects: 项目信息管理参数对象
        :return: 项目信息管理信息对象
        """
        projects_info = (
            (
                await db.execute(
                    select(RdProjects).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return projects_info

    @classmethod
    async def get_projects_list(cls, db: AsyncSession, query_object: ProjectsPageQueryModel, is_page: bool = False, current_user_id: int = None):
        """
        根据查询参数获取项目信息管理列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :param current_user_id: 当前用户ID，用于权限过滤
        :return: 项目信息管理列表信息对象
        """
        # 基础查询条件
        base_conditions = [
            RdProjects.project_name.like(f'%{query_object.project_name}%') if query_object.project_name else True,
            RdProjects.description == query_object.description if query_object.description else True,
            RdProjects.owner_id == query_object.owner_id if query_object.owner_id else True,
            RdProjects.is_deleted == False,
            RdProjects.created_at == query_object.created_at if query_object.created_at else True,
            RdProjects.updated_at == query_object.updated_at if query_object.updated_at else True,
        ]
        
        # 如果提供了当前用户ID，添加权限过滤
        if current_user_id is not None:
            # 导入项目成员表
            from sqlalchemy import text
            from module_business.entity.do.project_members_do import RdProjectMembers
            
            # 查询用户参与的项目ID列表（包括作为所有者和成员的项目）
            member_projects_query = select(RdProjectMembers.project_id).where(
                RdProjectMembers.user_id == current_user_id,
                RdProjectMembers.is_deleted == False
            )
            
            # 添加权限过滤条件：用户是项目所有者 OR 用户是项目成员
            # 注意：这里需要确保权限过滤条件正确应用
            permission_condition = (
                (RdProjects.owner_id == current_user_id) | 
                (RdProjects.project_id.in_(member_projects_query))
            )
            base_conditions.append(permission_condition)

        query = (
            select(RdProjects)
            .where(*base_conditions)
            .distinct()
        )

        # 添加动态排序
        if query_object.order_by_column:
            # 将驼峰命名转换为下划线命名
            order_column = ''.join(['_' + c.lower() if c.isupper() else c for c in query_object.order_by_column]).lstrip('_')
            # 获取排序字段
            order_field = getattr(RdProjects, order_column, None)
            if order_field is not None:
                if query_object.is_asc:
                    query = query.order_by(order_field.asc())
                else:
                    query = query.order_by(order_field.desc())
        else:
            # 默认按更新时间倒序
            query = query.order_by(RdProjects.updated_at.desc())

        projects_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return projects_list

    @classmethod
    async def add_projects_dao(cls, db: AsyncSession, projects: ProjectsModel):
        """
        新增项目信息管理信息

        :param db: orm对象
        :param projects: 项目信息管理参数对象
        :return: 项目信息管理信息对象
        """
        db_projects = RdProjects(**projects.model_dump())
        db.add(db_projects)
        await db.flush()
        return db_projects

    @classmethod
    async def edit_projects_dao(cls, db: AsyncSession, projects: dict):
        """
        编辑项目信息管理信息

        :param db: orm对象
        :param projects: 项目信息管理参数对象
        :return: 项目信息管理信息对象
        """
        await db.execute(
            update(RdProjects)
            .where(RdProjects.project_id == projects['project_id'])
            .values(**projects)
        )

    @classmethod
    async def delete_projects_dao(cls, db: AsyncSession, projects: ProjectsModel):
        """
        删除项目信息管理信息

        :param db: orm对象
        :param projects: 项目信息管理参数对象
        :return: 项目信息管理信息对象
        """
        await db.execute(
            delete(RdProjects)
            .where(RdProjects.project_id == projects.project_id)
        )

