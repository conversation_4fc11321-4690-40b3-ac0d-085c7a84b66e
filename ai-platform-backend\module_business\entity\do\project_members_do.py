from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, BigInteger, Enum
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class RdProjectMembers(Base):
    """
    项目成员表
    """
    __tablename__ = 'rd_project_members'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    project_id = Column(BigInteger, nullable=False, comment='项目ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    role_type = Column(Enum('manager', 'member'), nullable=False, default='member', comment='角色类型：manager-管理员，member-成员')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    is_deleted = Column(<PERSON><PERSON><PERSON>, default=False, comment='是否删除')