# AI平台权限管理系统

## 概述

本权限管理系统基于RBAC（基于角色的访问控制）模型实现，支持菜单级、功能级、数据级的细粒度权限控制。

## 角色定义

### 1. 超级管理员 (admin)
- **权限**：拥有所有权限 (`*:*:*`)
- **功能**：
  - 系统管理
  - 用户角色分配
  - 所有项目和任务的管理
  - 所有数据库的管理

### 2. 项目管理员 (project_manager)
- **权限**：管理负责的项目和团队
- **功能**：
  - 访问：首页、工具广场、项目管理、数据库管理
  - 项目：创建、编辑、删除自己负责的项目
  - 成员：管理项目成员
  - 任务：管理项目下所有任务
  - 数据库：管理项目相关的数据库

### 3. 项目成员 (project_member)
- **权限**：参与项目任务执行
- **功能**：
  - 访问：首页、工具广场、项目管理
  - 项目：查看所属项目
  - 任务：创建新任务，编辑/删除自己的任务，查看项目内所有任务
  - 数据：下载任务结果
  - **限制**：无数据库管理权限

### 4. 普通用户 (normal_user)
- **权限**：基础功能访问
- **功能**：
  - 访问：首页、工具广场
  - **限制**：无项目管理和数据库管理权限

## 系统架构

### 后端架构
```
权限控制层
├── 认证装饰器 (EnhancedAuth)
├── 项目权限验证 (ProjectAuth)
├── 任务权限验证 (TaskAuth)
└── 数据权限过滤器 (DataScopeFilter)

权限服务层
├── 权限管理服务 (PermissionService)
├── 项目权限服务 (ProjectAuthService)
└── 任务权限服务 (TaskAuthService)

数据访问层
├── 用户权限查询
├── 项目成员管理
└── 权限缓存
```

### 前端架构
```
路由权限控制
├── 菜单访问权限
├── 路由守卫
└── 动态路由生成

组件权限控制
├── 指令级权限 (v-hasPermi)
├── 角色权限 (v-hasRole)
└── 功能权限检查
```

## 数据库设计

### 核心表结构
```sql
-- 用户表
sys_user (user_id, user_name, ...)

-- 角色表  
sys_role (role_id, role_name, role_key, ...)

-- 菜单权限表
sys_menu (menu_id, menu_name, perms, ...)

-- 用户角色关联表
sys_user_role (user_id, role_id)

-- 角色菜单关联表
sys_role_menu (role_id, menu_id)

-- 项目成员表
project_members (id, project_id, user_id, role_type)
```

## 部署和配置

### 1. 数据库初始化

```bash
# 执行SQL脚本
psql -h localhost -U username -d database -f ai-platform-backend/sql/insert_new_roles.sql
```

### 2. 权限初始化

```bash
# 进入后端目录
cd ai-platform-backend

# 检查权限配置
python utils/init_permissions.py check

# 初始化角色权限
python utils/init_permissions.py init

# 执行完整初始化
python utils/init_permissions.py all
```

### 3. 配置验证

```python
# 验证用户权限
from module_admin.service.permission_service import PermissionService

# 检查用户权限
permissions = await PermissionService.get_user_permissions(db, user_id)
print(f"用户权限: {permissions}")

# 检查用户角色
roles = await PermissionService.get_user_roles(db, user_id)
print(f"用户角色: {roles}")
```

## 使用示例

### 后端控制器权限控制

```python
from module_admin.aspect.enhanced_auth import EnhancedAuth, ProjectAuth
from config.role_constants import PermissionConstants

# 菜单权限
@EnhancedAuth.require_menu_access('project')
async def get_project_list():
    pass

# 功能权限
@EnhancedAuth.require_permission(PermissionConstants.PROJECT_CREATE)
async def create_project():
    pass

# 项目权限
@ProjectAuth.require_project_access(require_manager=True)
async def edit_project(project_id: int):
    pass
```

### 前端权限控制

```vue
<template>
  <!-- 菜单级权限 -->
  <router-link to="/projects" v-if="$auth.hasPermi(['menu:project'])">
    项目管理
  </router-link>

  <!-- 功能级权限 -->
  <el-button 
    v-hasPermi="['project:create']"
    @click="createProject">
    创建项目
  </el-button>

  <!-- 角色级权限 -->
  <div v-hasRole="['project_manager']">
    项目管理员专用功能
  </div>

  <!-- 动态权限检查 -->
  <el-button 
    v-if="canEditProject"
    @click="editProject">
    编辑项目
  </el-button>
</template>

<script>
export default {
  computed: {
    canEditProject() {
      // 动态检查项目编辑权限
      return this.checkProjectPermission(this.projectId, 'edit')
    }
  }
}
</script>
```

## 权限配置

### 权限常量定义
```python
# config/role_constants.py

class PermissionConstants:
    # 菜单权限
    MENU_HOME = 'menu:home'
    MENU_TOOLS = 'menu:tools' 
    MENU_PROJECT = 'menu:project'
    MENU_DATABASE = 'menu:database'
    
    # 项目权限
    PROJECT_LIST = 'project:list'
    PROJECT_CREATE = 'project:create'
    PROJECT_EDIT = 'project:edit'
    PROJECT_DELETE = 'project:delete'
    
    # 任务权限
    TASK_LIST = 'task:list'
    TASK_CREATE = 'task:create'
    TASK_EDIT_OWN = 'task:edit:own'
    TASK_EDIT_ALL = 'task:edit:all'
```

### 角色权限映射
```python
ROLE_PERMISSIONS = {
    RoleConstants.SUPER_ADMIN: [
        PermissionConstants.ALL_PERMISSION
    ],
    
    RoleConstants.PROJECT_MANAGER: [
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
        PermissionConstants.MENU_PROJECT,
        PermissionConstants.MENU_DATABASE,
        # ... 更多权限
    ],
    
    # ... 其他角色
}
```

## API接口

### 权限检查接口
```bash
# 检查项目权限
GET /api/projects/check-permission/{project_id}

# 检查任务权限  
GET /api/tasks/check-permission/{task_id}

# 检查数据库权限
GET /api/knowledge-bases/check-permission/{kb_id}
```

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "can_view": true,
    "can_edit": false,
    "can_delete": false,
    "can_manage_members": false
  }
}
```

## 监控和日志

### 权限日志
```python
# 权限验证失败日志
logger.warning(f'用户 {user_id} 访问 {resource} 权限不足')

# 权限检查成功日志
logger.info(f'用户 {user_id} 成功访问 {resource}')
```

### 审计日志
- 登录日志：记录用户登录信息
- 操作日志：记录关键操作和权限使用
- 错误日志：记录权限验证失败

## 扩展和维护

### 添加新权限
1. 在 `PermissionConstants` 中定义新权限
2. 在 `ROLE_PERMISSIONS` 中分配给相应角色
3. 在数据库中添加对应的菜单记录
4. 运行权限初始化脚本

### 添加新角色
1. 在 `RoleConstants` 中定义新角色
2. 在 `ROLE_PERMISSIONS` 中配置角色权限
3. 在数据库中添加角色记录
4. 更新前后端权限控制逻辑

## 故障排除

### 常见问题

1. **权限验证失败**
   - 检查用户角色分配
   - 验证权限配置
   - 查看错误日志

2. **菜单不显示**
   - 确认角色权限配置
   - 检查前端路由权限
   - 验证菜单权限标识

3. **数据权限异常**
   - 检查项目成员配置
   - 验证数据过滤逻辑
   - 确认权限范围设置

### 调试工具
```bash
# 检查权限配置
python utils/init_permissions.py check

# 重新初始化权限
python utils/init_permissions.py init

# 查看用户权限
SELECT * FROM sys_user_role WHERE user_id = ?;
SELECT * FROM sys_role_menu WHERE role_id = ?;
```

## 联系和支持

如有问题，请查看：
1. 系统日志：`ai-platform-backend/logs/`
2. 错误日志：查看应用程序错误输出
3. 数据库日志：检查数据库连接和查询

---

**版本**：v1.0  
**更新时间**：2025年1月  
**维护者**：AI平台开发团队