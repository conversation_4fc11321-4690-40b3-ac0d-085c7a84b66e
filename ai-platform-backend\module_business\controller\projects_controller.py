from fastapi import APIRouter, Depends, Form, Request, Query
from pydantic_validation_decorator import <PERSON>idate<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.projects_service import ProjectsService
from module_business.service.knowledge_bases_service import KnowledgeBasesService
from module_business.entity.vo.projects_vo import ProjectsModel, ProjectsPageQueryModel
from module_business.entity.vo.knowledge_bases_vo import KnowledgeBasesModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime
from typing import List
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder


projectsController = APIRouter(prefix='/business/projects', dependencies=[Depends(LoginService.get_current_user)])


@projectsController.get(
    '/list', response_model=PageResponseModel
    # , dependencies=[Depends(CheckUserInterfaceAuth('business:projects:list'))]
)
async def get_business_projects_list(
    request: Request,
    projects_page_query: ProjectsPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取项目列表
    :param request: 请求对象
    :param projects_page_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 项目列表
    """
    # 设置默认排序为按更新时间倒序
    if not projects_page_query.order_by_column:
        projects_page_query.order_by_column = 'updated_at'
        projects_page_query.is_asc = False
        
    # 获取分页数据，传递当前用户ID进行权限过滤
    projects_page_query_result = await ProjectsService.get_projects_list_services(
        query_db, 
        projects_page_query, 
        is_page=True, 
        current_user_id=current_user.user.user_id
    )
    logger.info('获取项目列表成功')

    return ResponseUtil.success(model_content=projects_page_query_result)


@projectsController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:add'))]
@ValidateFields(validate_model='add_projects')
@Log(title='项目管理', business_type=BusinessType.INSERT)
async def add_business_projects(
    request: Request,
    add_projects: ProjectsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    try:
        # 设置创建者信息
        add_projects.created_at = datetime.now()
        add_projects.updated_at = datetime.now()
        add_projects.owner_id = current_user.user.user_id
        
        # 添加项目
        add_projects_result = await ProjectsService.add_projects_services(query_db, add_projects)

        # 自动将创建者添加为项目成员（owner角色）
        from module_business.service.project_members_service import ProjectMembersService
        success, message = await ProjectMembersService.add_project_member(
            query_db,
            add_projects_result.result.project_id,
            current_user.user.user_id,
            'owner',  # 创建者默认为owner角色
            current_user.user.user_id
        )

        if not success:
            logger.warning(f'添加项目创建者为成员失败: {message}')

        # 同步创建数据库
        kb_model = KnowledgeBasesModel(
            kbName=add_projects.project_name+"数据库",  # 使用项目名称作为数据库名称
            projectId=add_projects_result.result.project_id,  # 使用新创建的项目ID
            description=add_projects.description,  # 使用项目描述作为数据库描述
            ownerId=current_user.user.user_id,
            createdAt=datetime.now(),
            updatedAt=datetime.now()
        )
        await KnowledgeBasesService.add_knowledge_bases_services(query_db, kb_model)

        # 提交所有操作的事务
        await query_db.commit()
        
        logger.info(add_projects_result.message)
        
        # 返回创建的项目数据
        from utils.common_util import CamelCaseUtil
        project_data = CamelCaseUtil.transform_result(add_projects_result.result)
        
        return ResponseUtil.success(msg=add_projects_result.message, data=project_data)
    except Exception as e:
        # 回滚事务
        await query_db.rollback()
        raise e


@projectsController.put('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:edit'))]
@ValidateFields(validate_model='edit_projects')
@Log(title='项目信息管理', business_type=BusinessType.UPDATE)
async def edit_business_projects(
    request: Request,
    edit_projects: ProjectsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # edit_projects.update_by = current_user.user.user_name
    edit_projects_result = await ProjectsService.edit_projects_services(query_db, edit_projects)
    return ResponseUtil.success(msg=edit_projects_result.message)


@projectsController.get('/list_with_types', summary='获取带类型标签的项目列表')
async def get_projects_list_with_types(
    request: Request,
    projects_page_query: ProjectsPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取带类型标签的项目列表
    """
    # 设置默认排序为按更新时间倒序
    if not projects_page_query.order_by_column:
        projects_page_query.order_by_column = 'updated_at'
        projects_page_query.is_asc = False
        
    # 获取分页数据，传递当前用户ID进行权限过滤
    projects_page_query_result = await ProjectsService.get_projects_list_services(
        query_db, 
        projects_page_query, 
        is_page=True, 
        current_user_id=current_user.user.user_id
    )
    
    # 这里可以添加类型标签的处理逻辑
    logger.info('获取带类型标签的项目列表成功')
    return ResponseUtil.success(model_content=projects_page_query_result)


@projectsController.delete('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:remove'))]
@ValidateFields(validate_model='delete_projects')
@Log(title='项目信息管理', business_type=BusinessType.DELETE)
async def delete_business_projects(
    request: Request,
    delete_projects: ProjectsModel,
    query_db: AsyncSession = Depends(get_db),
):
    delete_projects_result = await ProjectsService.delete_projects_services(query_db, delete_projects)
    return ResponseUtil.success(msg=delete_projects_result.message)


@projectsController.get(
    '/{project_id}', response_model=ProjectsModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:query'))]
)
async def query_detail_business_projects(request: Request, project_id: int, query_db: AsyncSession = Depends(get_db)):
    """
    根据项目ID获取项目详细信息
    """
    projects_detail_result = await ProjectsService.projects_detail_services(query_db, project_id)
    return ResponseUtil.success(data=projects_detail_result)


@projectsController.get('/check-permission/{project_id}')
async def check_project_permission(
    request: Request, 
    project_id: int, 
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    检查用户对项目的权限
    """
    try:
        # 检查项目是否存在
        project = await ProjectsService.projects_detail_services(query_db, project_id)
        if not project:
            return ResponseUtil.error(msg="项目不存在")
        
        # 检查用户权限
        from module_business.dao.projects_dao import ProjectsDao
        from module_business.entity.vo.projects_vo import ProjectsPageQueryModel
        
        # 创建查询对象
        query = ProjectsPageQueryModel()
        query.owner_id = project_id  # 临时使用owner_id字段来传递project_id
        
        # 检查用户是否有权限访问此项目
        user_projects = await ProjectsDao.get_projects_list(
            query_db, 
            query, 
            is_page=False, 
            current_user_id=current_user.user.user_id
        )
        
        # 检查项目是否在用户有权限的项目列表中
        has_permission = any(p.project_id == project_id for p in user_projects.rows) if hasattr(user_projects, 'rows') else False
        
        if has_permission:
            return ResponseUtil.success(msg="有权限访问此项目", data={"has_permission": True})
        else:
            return ResponseUtil.error(msg="无权限访问此项目", data={"has_permission": False})
            
    except Exception as e:
        logger.error(f"检查项目权限失败: {str(e)}")
        return ResponseUtil.error(msg="检查权限失败")
