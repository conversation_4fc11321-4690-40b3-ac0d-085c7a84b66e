"""
数据库管理控制器 - 应用新的权限控制
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.aspect.enhanced_auth import EnhancedAuth
from module_admin.aspect.data_scope_enhanced import DataScopeFilter
from module_business.entity.vo.knowledge_bases_vo import (
    KnowledgeBasePageQueryModel, AddKnowledgeBaseModel, EditKnowledgeBaseModel, DeleteKnowledgeBaseModel
)
from module_business.service.knowledge_bases_service import KnowledgeBasesService
from utils.response_util import ResponseUtil
from utils.log_util import logger
from config.role_constants import PermissionConstants, RoleConstants

knowledgeBaseController = APIRouter()

@knowledgeBaseController.get('/list')
@EnhancedAuth.require_menu_access('database')
async def get_knowledge_base_list(
    request: Request,
    query_params: KnowledgeBasePageQueryModel = Depends(KnowledgeBasePageQueryModel.as_query()),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取数据库列表 - 应用数据权限过滤"""
    try:
        # 项目成员无数据库访问权限
        if RoleConstants.PROJECT_MEMBER in current_user.roles:
            return ResponseUtil.error(msg='项目成员无数据库访问权限')
        
        # 普通用户无数据库访问权限
        if RoleConstants.NORMAL_USER in current_user.roles:
            return ResponseUtil.error(msg='普通用户无数据库访问权限')
        
        # 应用数据权限过滤
        kb_result = await KnowledgeBasesService.get_knowledge_base_list_services(
            query_db, query_params, current_user
        )
        logger.info('获取数据库列表成功')
        return ResponseUtil.success(data=kb_result)
    except Exception as e:
        logger.error(f'获取数据库列表失败: {e}')
        return ResponseUtil.error(msg=f'获取数据库列表失败: {str(e)}')

@knowledgeBaseController.post('/add')
@EnhancedAuth.require_permission(PermissionConstants.DATABASE_CREATE)
async def add_knowledge_base(
    request: Request,
    add_kb: AddKnowledgeBaseModel,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """新增数据库"""
    try:
        # 检查项目访问权限
        if add_kb.project_id:
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            has_access = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, add_kb.project_id, require_manager=True
            )
            if not has_access:
                return ResponseUtil.error(msg='无项目管理权限')
        
        # 设置创建者
        add_kb.create_by = current_user.user.user_name
        
        add_kb_result = await KnowledgeBasesService.add_knowledge_base_services(query_db, add_kb)
        logger.info('新增数据库成功')
        return ResponseUtil.success(data=add_kb_result, msg=add_kb_result.message)
    except Exception as e:
        logger.error(f'新增数据库失败: {e}')
        return ResponseUtil.error(msg=f'新增数据库失败: {str(e)}')

@knowledgeBaseController.put('/{kb_id}')
@EnhancedAuth.require_permission(PermissionConstants.DATABASE_EDIT)
async def edit_knowledge_base(
    request: Request,
    kb_id: int,
    edit_kb: EditKnowledgeBaseModel,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """编辑数据库 - 需要数据库编辑权限"""
    try:
        # 获取数据库信息检查权限
        from module_business.dao.knowledge_bases_dao import KnowledgeBasesDao
        kb = await KnowledgeBasesDao.get_knowledge_bases_detail_by_id(query_db, kb_id)
        if not kb:
            return ResponseUtil.error(msg='数据库不存在')
        
        # 检查项目访问权限
        if kb.project_id:
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            has_access = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, kb.project_id, require_manager=True
            )
            if not has_access:
                return ResponseUtil.error(msg='无项目管理权限')
        
        edit_kb.kb_id = kb_id
        edit_kb.update_by = current_user.user.user_name
        
        edit_kb_result = await KnowledgeBasesService.edit_knowledge_base_services(query_db, edit_kb)
        logger.info('编辑数据库成功')
        return ResponseUtil.success(data=edit_kb_result, msg=edit_kb_result.message)
    except Exception as e:
        logger.error(f'编辑数据库失败: {e}')
        return ResponseUtil.error(msg=f'编辑数据库失败: {str(e)}')

@knowledgeBaseController.delete('/{kb_id}')
@EnhancedAuth.require_permission(PermissionConstants.DATABASE_DELETE)
async def delete_knowledge_base(
    request: Request,
    kb_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """删除数据库 - 需要数据库删除权限"""
    try:
        # 获取数据库信息检查权限
        from module_business.dao.knowledge_bases_dao import KnowledgeBasesDao
        kb = await KnowledgeBasesDao.get_knowledge_bases_detail_by_id(query_db, kb_id)
        if not kb:
            return ResponseUtil.error(msg='数据库不存在')
        
        # 检查项目访问权限
        if kb.project_id:
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            has_access = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, kb.project_id, require_manager=True
            )
            if not has_access:
                return ResponseUtil.error(msg='无项目管理权限')
        
        delete_kb = DeleteKnowledgeBaseModel(
            kb_ids=str(kb_id),
            update_by=current_user.user.user_name
        )
        
        delete_kb_result = await KnowledgeBasesService.delete_knowledge_base_services(query_db, delete_kb)
        logger.info('删除数据库成功')
        return ResponseUtil.success(data=delete_kb_result, msg=delete_kb_result.message)
    except Exception as e:
        logger.error(f'删除数据库失败: {e}')
        return ResponseUtil.error(msg=f'删除数据库失败: {str(e)}')

@knowledgeBaseController.get('/{kb_id}')
@EnhancedAuth.require_permission(PermissionConstants.DATABASE_LIST)
async def get_knowledge_base_detail(
    request: Request,
    kb_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取数据库详情"""
    try:
        # 获取数据库信息检查权限
        from module_business.dao.knowledge_bases_dao import KnowledgeBasesDao
        kb = await KnowledgeBasesDao.get_knowledge_bases_detail_by_id(query_db, kb_id)
        if not kb:
            return ResponseUtil.error(msg='数据库不存在')
        
        # 检查项目访问权限
        if kb.project_id:
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            has_access = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, kb.project_id, require_manager=False
            )
            if not has_access:
                return ResponseUtil.error(msg='无项目访问权限')
        
        kb_detail = await KnowledgeBasesService.get_knowledge_base_detail_services(query_db, kb_id)
        logger.info('获取数据库详情成功')
        return ResponseUtil.success(data=kb_detail)
    except Exception as e:
        logger.error(f'获取数据库详情失败: {e}')
        return ResponseUtil.error(msg=f'获取数据库详情失败: {str(e)}')

@knowledgeBaseController.get('/project/{project_id}')
@EnhancedAuth.require_permission(PermissionConstants.DATABASE_LIST)
async def get_project_knowledge_bases(
    request: Request,
    project_id: int,
    query_params: KnowledgeBasePageQueryModel = Depends(KnowledgeBasePageQueryModel.as_query()),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取项目数据库列表"""
    try:
        # 检查项目访问权限
        from module_admin.aspect.enhanced_auth import ProjectAuthService
        has_access = await ProjectAuthService.check_project_access(
            query_db, current_user.user.user_id, project_id, require_manager=False
        )
        if not has_access:
            return ResponseUtil.error(msg='无项目访问权限')
        
        # 设置项目ID过滤
        query_params.project_id = project_id
        kb_result = await KnowledgeBasesService.get_knowledge_base_list_services(
            query_db, query_params, current_user
        )
        logger.info('获取项目数据库列表成功')
        return ResponseUtil.success(data=kb_result)
    except Exception as e:
        logger.error(f'获取项目数据库列表失败: {e}')
        return ResponseUtil.error(msg=f'获取项目数据库列表失败: {str(e)}')

@knowledgeBaseController.get('/check-permission/{kb_id}')
async def check_knowledge_base_permission(
    request: Request,
    kb_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """检查数据库权限 - 供前端使用"""
    try:
        # 项目成员和普通用户无数据库访问权限
        if (RoleConstants.PROJECT_MEMBER in current_user.roles or 
            RoleConstants.NORMAL_USER in current_user.roles):
            permissions = {
                'can_view': False,
                'can_edit': False,
                'can_delete': False,
                'can_manage_files': False
            }
            return ResponseUtil.success(data=permissions)
        
        # 获取数据库信息
        from module_business.dao.knowledge_bases_dao import KnowledgeBasesDao
        kb = await KnowledgeBasesDao.get_knowledge_bases_detail_by_id(query_db, kb_id)
        if not kb:
            return ResponseUtil.error(msg='数据库不存在')
        
        # 检查项目权限
        can_view = False
        can_manage = False
        
        if kb.project_id:
            from module_admin.aspect.enhanced_auth import ProjectAuthService
            can_view = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, kb.project_id, require_manager=False
            )
            can_manage = await ProjectAuthService.check_project_access(
                query_db, current_user.user.user_id, kb.project_id, require_manager=True
            )
        
        permissions = {
            'can_view': can_view and PermissionConstants.DATABASE_LIST in current_user.permissions,
            'can_edit': can_manage and PermissionConstants.DATABASE_EDIT in current_user.permissions,
            'can_delete': can_manage and PermissionConstants.DATABASE_DELETE in current_user.permissions,
            'can_manage_files': can_manage
        }
        
        logger.info('检查数据库权限成功')
        return ResponseUtil.success(data=permissions)
    except Exception as e:
        logger.error(f'检查数据库权限失败: {e}')
        return ResponseUtil.error(msg=f'检查数据库权限失败: {str(e)}')