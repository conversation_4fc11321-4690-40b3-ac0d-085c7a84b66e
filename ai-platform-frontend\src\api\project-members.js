import request from '@/utils/request'

// 获取项目成员列表
export function getProjectMembers(projectId) {
  return request({
    url: `/business/project-members/list/${projectId}`,
    method: 'get'
  })
}

// 获取可添加的用户列表
export function getAvailableUsers(projectId) {
  return request({
    url: '/business/project-members/available-users',
    method: 'get',
    params: { project_id: projectId }
  })
}

// 添加项目成员
export function addProjectMember(projectId, userId, roleType = 'member') {
  return request({
    url: '/business/project-members/add',
    method: 'post',
    params: { project_id: projectId, user_id: userId, role_type: roleType }
  })
}

// 更新成员角色
export function updateProjectMemberRole(projectId, userId, roleType) {
  return request({
    url: '/business/project-members/update-role',
    method: 'put',
    params: { project_id: projectId, user_id: userId, role_type: roleType }
  })
}

// 移除项目成员
export function removeProjectMember(projectId, userId) {
  return request({
    url: '/business/project-members/remove',
    method: 'delete',
    params: { project_id: projectId, user_id: userId }
  })
}