# 数据库迁移指南

## 问题描述

在项目创建时，系统尝试将项目创建者添加为项目成员时出现以下错误：

```
(asyncmy.errors.DataError) (1265, "Data truncated for column 'role_type' at row 1")
```

## 根本原因

数据库表 `rd_project_members` 中的 `role_type` 字段定义为：
```sql
role_type ENUM('manager', 'member') NOT NULL DEFAULT 'member'
```

但是代码中尝试插入 `'owner'` 值，该值不在允许的枚举值列表中。

## 解决方案

### 方案1：执行数据库迁移（推荐）

1. **执行迁移脚本**：
   ```bash
   mysql -u your_username -p your_database < sql/update_project_members_role_type.sql
   ```

2. **迁移脚本功能**：
   - 将 `role_type` 枚举值更新为 `ENUM('owner', 'manager', 'member')`
   - 将现有项目所有者的角色更新为 `'owner'`
   - 为没有在成员表中的项目所有者添加记录

3. **验证迁移结果**：
   ```sql
   SELECT 
       p.project_id,
       p.project_name,
       p.owner_id,
       pm.role_type,
       u.user_name as owner_name
   FROM rd_projects p
   LEFT JOIN rd_project_members pm ON p.project_id = pm.project_id AND p.owner_id = pm.user_id AND pm.is_deleted = 0
   LEFT JOIN sys_user u ON p.owner_id = u.user_id
   WHERE p.is_deleted = 0
   ORDER BY p.project_id;
   ```

### 方案2：临时修复（已实施）

如果暂时无法执行数据库迁移，代码已经临时修改为使用 `'manager'` 角色：

- 项目创建者将被添加为 `'manager'` 角色而不是 `'owner'`
- 这是一个临时解决方案，建议尽快执行数据库迁移

## 代码更改

### 后端更改

1. **数据模型更新** (`module_business/entity/do/project_members_do.py`)：
   ```python
   role_type = Column(Enum('owner', 'manager', 'member'), nullable=False, default='member', 
                     comment='角色类型：owner-所有者，manager-管理员，member-成员')
   ```

2. **控制器临时修复**：
   - `projects_controller.py`: 使用 `'manager'` 角色
   - `projects_controller_updated.py`: 使用 `'manager'` 角色

### 前端更改

1. **角色显示更新** (`src/views/projects/members.vue`)：
   - 添加了对 `'owner'` 角色的支持
   - 更新了角色标签颜色和文本显示

## 迁移后的角色层次

执行迁移后，项目成员将有三种角色：

1. **Owner (所有者)**：
   - 项目创建者
   - 拥有最高权限
   - 标签颜色：红色 (danger)

2. **Manager (管理员)**：
   - 项目管理员
   - 可以管理项目成员
   - 标签颜色：橙色 (warning)

3. **Member (成员)**：
   - 普通项目成员
   - 基本项目访问权限
   - 标签颜色：蓝色 (info)

## 注意事项

1. **备份数据库**：执行迁移前请备份数据库
2. **测试环境先行**：建议先在测试环境执行迁移
3. **权限验证**：迁移后验证各角色的权限是否正常工作
4. **回滚计划**：准备回滚脚本以防出现问题

## 回滚脚本

如果需要回滚到原来的状态：

```sql
-- 将所有 owner 角色改为 manager
UPDATE rd_project_members SET role_type = 'manager' WHERE role_type = 'owner';

-- 恢复原来的枚举定义
ALTER TABLE rd_project_members 
MODIFY COLUMN role_type ENUM('manager', 'member') NOT NULL DEFAULT 'member' 
COMMENT '角色类型：manager-管理员，member-成员';
```
