"""
权限管理服务
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from config.role_constants import RoleConstants, PermissionConstants, ROLE_PERMISSIONS
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.log_util import logger

class PermissionService:
    """权限管理服务"""
    
    @staticmethod
    async def assign_role_permissions(db: AsyncSession, role_key: str) -> bool:
        """为角色分配权限"""
        try:
            # 根据角色键获取权限
            permissions = ROLE_PERMISSIONS.get(role_key, [])
            if not permissions:
                logger.warning(f'角色 {role_key} 没有配置权限')
                return False
            
            # 获取角色信息
            role_query = text("""
                SELECT role_id FROM sys_role WHERE role_key = :role_key AND del_flag = '0'
            """)
            role_result = await db.execute(role_query, {"role_key": role_key})
            role = role_result.fetchone()
            
            if not role:
                logger.error(f'角色 {role_key} 不存在')
                return False
            
            role_id = role.role_id
            
            # 获取权限对应的菜单ID
            menu_ids = []
            
            # 超级管理员获取所有菜单权限
            if PermissionConstants.ALL_PERMISSION in permissions:
                menu_query = text("""
                    SELECT menu_id FROM sys_menu WHERE status = '0'
                """)
                menu_result = await db.execute(menu_query)
                menu_ids = [row.menu_id for row in menu_result.fetchall()]
            else:
                # 根据权限标识获取菜单ID
                for permission in permissions:
                    menu_query = text("""
                        SELECT menu_id FROM sys_menu WHERE perms = :perms AND status = '0'
                    """)
                    menu_result = await db.execute(menu_query, {"perms": permission})
                    menu_row = menu_result.fetchone()
                    if menu_row:
                        menu_ids.append(menu_row.menu_id)
            
            if not menu_ids:
                logger.warning(f'角色 {role_key} 没有找到对应的菜单权限')
                return False
            
            # 删除现有的角色菜单关联
            delete_query = text("""
                DELETE FROM sys_role_menu WHERE role_id = :role_id
            """)
            await db.execute(delete_query, {"role_id": role_id})
            
            # 插入新的角色菜单关联
            for menu_id in menu_ids:
                insert_query = text("""
                    INSERT INTO sys_role_menu (role_id, menu_id) VALUES (:role_id, :menu_id)
                """)
                await db.execute(insert_query, {"role_id": role_id, "menu_id": menu_id})
            
            await db.commit()
            logger.info(f'成功为角色 {role_key} 分配 {len(menu_ids)} 个菜单权限')
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f'为角色 {role_key} 分配权限失败: {e}')
            return False
    
    @staticmethod
    async def init_all_role_permissions(db: AsyncSession) -> bool:
        """初始化所有角色权限"""
        try:
            success_count = 0
            total_count = len(ROLE_PERMISSIONS)
            
            for role_key in ROLE_PERMISSIONS.keys():
                if await PermissionService.assign_role_permissions(db, role_key):
                    success_count += 1
            
            logger.info(f'角色权限初始化完成: {success_count}/{total_count}')
            return success_count == total_count
            
        except Exception as e:
            logger.error(f'初始化角色权限失败: {e}')
            return False
    
    @staticmethod
    async def check_user_permission(db: AsyncSession, user_id: int, permission: str) -> bool:
        """检查用户是否拥有特定权限"""
        try:
            # 检查用户角色权限
            permission_query = text("""
                SELECT DISTINCT m.perms
                FROM sys_user_role ur
                JOIN sys_role_menu rm ON ur.role_id = rm.role_id
                JOIN sys_menu m ON rm.menu_id = m.menu_id
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id 
                AND r.status = '0' 
                AND r.del_flag = '0'
                AND m.status = '0'
                AND m.perms IS NOT NULL 
                AND m.perms != ''
            """)
            
            result = await db.execute(permission_query, {"user_id": user_id})
            user_permissions = [row.perms for row in result.fetchall() if row.perms]
            
            # 检查是否有超级管理员权限
            if PermissionConstants.ALL_PERMISSION in user_permissions:
                return True
            
            # 检查具体权限
            return permission in user_permissions
            
        except Exception as e:
            logger.error(f'检查用户权限失败: {e}')
            return False
    
    @staticmethod
    async def get_user_permissions(db: AsyncSession, user_id: int) -> List[str]:
        """获取用户所有权限"""
        try:
            # 获取用户角色权限
            permission_query = text("""
                SELECT DISTINCT m.perms
                FROM sys_user_role ur
                JOIN sys_role_menu rm ON ur.role_id = rm.role_id
                JOIN sys_menu m ON rm.menu_id = m.menu_id
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id 
                AND r.status = '0' 
                AND r.del_flag = '0'
                AND m.status = '0'
                AND m.perms IS NOT NULL 
                AND m.perms != ''
            """)
            
            result = await db.execute(permission_query, {"user_id": user_id})
            permissions = [row.perms for row in result.fetchall()]
            
            # 检查是否是超级管理员
            role_query = text("""
                SELECT r.role_id
                FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id 
                AND r.role_id = 1
                AND r.status = '0' 
                AND r.del_flag = '0'
            """)
            
            role_result = await db.execute(role_query, {"user_id": user_id})
            is_admin = role_result.fetchone() is not None
            
            if is_admin:
                permissions.append(PermissionConstants.ALL_PERMISSION)
            
            return permissions
            
        except Exception as e:
            logger.error(f'获取用户权限失败: {e}')
            return []
    
    @staticmethod
    async def get_user_roles(db: AsyncSession, user_id: int) -> List[str]:
        """获取用户角色"""
        try:
            role_query = text("""
                SELECT r.role_key
                FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id 
                AND r.status = '0' 
                AND r.del_flag = '0'
            """)
            
            result = await db.execute(role_query, {"user_id": user_id})
            roles = [row.role_key for row in result.fetchall()]
            
            return roles
            
        except Exception as e:
            logger.error(f'获取用户角色失败: {e}')
            return []
    
    @staticmethod
    async def assign_user_role(db: AsyncSession, user_id: int, role_key: str) -> bool:
        """为用户分配角色"""
        try:
            # 获取角色ID
            role_query = text("""
                SELECT role_id FROM sys_role WHERE role_key = :role_key AND del_flag = '0'
            """)
            role_result = await db.execute(role_query, {"role_key": role_key})
            role = role_result.fetchone()
            
            if not role:
                logger.error(f'角色 {role_key} 不存在')
                return False
            
            role_id = role.role_id
            
            # 检查用户角色关联是否已存在
            check_query = text("""
                SELECT 1 FROM sys_user_role WHERE user_id = :user_id AND role_id = :role_id
            """)
            check_result = await db.execute(check_query, {"user_id": user_id, "role_id": role_id})
            
            if check_result.fetchone():
                logger.info(f'用户 {user_id} 已拥有角色 {role_key}')
                return True
            
            # 插入用户角色关联
            insert_query = text("""
                INSERT INTO sys_user_role (user_id, role_id) VALUES (:user_id, :role_id)
            """)
            await db.execute(insert_query, {"user_id": user_id, "role_id": role_id})
            await db.commit()
            
            logger.info(f'成功为用户 {user_id} 分配角色 {role_key}')
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f'为用户分配角色失败: {e}')
            return False
    
    @staticmethod
    async def remove_user_role(db: AsyncSession, user_id: int, role_key: str) -> bool:
        """移除用户角色"""
        try:
            # 获取角色ID
            role_query = text("""
                SELECT role_id FROM sys_role WHERE role_key = :role_key AND del_flag = '0'
            """)
            role_result = await db.execute(role_query, {"role_key": role_key})
            role = role_result.fetchone()
            
            if not role:
                logger.error(f'角色 {role_key} 不存在')
                return False
            
            role_id = role.role_id
            
            # 删除用户角色关联
            delete_query = text("""
                DELETE FROM sys_user_role WHERE user_id = :user_id AND role_id = :role_id
            """)
            result = await db.execute(delete_query, {"user_id": user_id, "role_id": role_id})
            await db.commit()
            
            if result.rowcount > 0:
                logger.info(f'成功移除用户 {user_id} 的角色 {role_key}')
                return True
            else:
                logger.warning(f'用户 {user_id} 没有角色 {role_key}')
                return False
                
        except Exception as e:
            await db.rollback()
            logger.error(f'移除用户角色失败: {e}')
            return False
    
    @staticmethod
    def get_role_permissions_config() -> dict:
        """获取角色权限配置"""
        return ROLE_PERMISSIONS
    
    @staticmethod
    def validate_permission(permission: str) -> bool:
        """验证权限标识是否有效"""
        # 检查是否是预定义的权限常量
        valid_permissions = []
        for attr_name in dir(PermissionConstants):
            if not attr_name.startswith('_'):
                valid_permissions.append(getattr(PermissionConstants, attr_name))
        
        return permission in valid_permissions